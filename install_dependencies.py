#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFX VTK可视化依赖安装脚本
"""

import subprocess
import sys
import importlib


def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False


def install_package(package_name):
    """安装包"""
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name,
            '-i', 'http://***********:8081/repository/crealife-pypi/simple', '--trusted-host', '***********'])
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package_name} 安装失败")
        return False


def main():
    """主函数"""
    print("CFX VTK可视化工具依赖检查和安装")
    print("=" * 50)
    
    # 必需的包列表
    required_packages = [
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("vtk", "vtk"),
    ]

    # Qt相关包（二选一）
    qt_packages = [
        ("PyQt5", "PyQt5"),
        ("PyQt6", "PyQt6"),
    ]

    # 可选的包列表
    optional_packages = [
        ("matplotlib", "matplotlib"),
        ("scipy", "scipy"),
    ]
    
    missing_packages = []
    
    # 检查必需包
    print("\n检查必需依赖:")
    for package_name, import_name in required_packages:
        if check_package(package_name, import_name):
            print(f"✓ {package_name} 已安装")
        else:
            print(f"✗ {package_name} 未安装")
            missing_packages.append(package_name)
    
    # 检查Qt包
    print("\n检查Qt依赖 (GUI界面需要):")
    qt_available = False
    for package_name, import_name in qt_packages:
        if check_package(package_name, import_name):
            print(f"✓ {package_name} 已安装")
            qt_available = True
            break
        else:
            print(f"- {package_name} 未安装")

    if not qt_available:
        print("⚠ 未安装Qt库，GUI版本将无法使用")
        missing_packages.append("PyQt5")  # 默认推荐PyQt5

    # 检查可选包
    print("\n检查可选依赖:")
    for package_name, import_name in optional_packages:
        if check_package(package_name, import_name):
            print(f"✓ {package_name} 已安装")
        else:
            print(f"- {package_name} 未安装 (可选)")
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n需要安装 {len(missing_packages)} 个包:")
        for package in missing_packages:
            print(f"  - {package}")
        
        response = input("\n是否现在安装? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print("\n开始安装...")
            success_count = 0
            for package in missing_packages:
                if install_package(package):
                    success_count += 1
            
            print(f"\n安装完成: {success_count}/{len(missing_packages)} 个包安装成功")
            
            if success_count == len(missing_packages):
                print("✓ 所有依赖已安装完成!")
            else:
                print("⚠ 部分依赖安装失败，请手动安装")
        else:
            print("跳过安装")
    else:
        print("\n✓ 所有必需依赖已安装!")
    
    # 版本信息
    print("\n版本信息:")
    try:
        import numpy
        print(f"  NumPy: {numpy.__version__}")
    except ImportError:
        print("  NumPy: 未安装")
    
    try:
        import pandas
        print(f"  Pandas: {pandas.__version__}")
    except ImportError:
        print("  Pandas: 未安装")
    
    try:
        import vtk
        print(f"  VTK: {vtk.vtkVersion.GetVTKVersion()}")
    except ImportError:
        print("  VTK: 未安装")
    
    print("\n使用说明:")
    print("1. 运行Qt GUI版本: python vtk_flow_visualizer_qt.py")
    print("2. 运行Tkinter版本: python vtk_flow_visualizer.py")
    print("3. 运行快速可视化: python quick_visualize.py")
    print("4. 确保demo_data/case1目录包含所需的CSV文件")


if __name__ == "__main__":
    main()
