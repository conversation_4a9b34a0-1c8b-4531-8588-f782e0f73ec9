#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度管理器
负责管理进度对话框和进度回调
"""
import time

try:
    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QWidget, QProgressBar, \
        QPushButton, QTableWidget, QApplication, QHBoxLayout, QTableWidgetItem, QMessageBox
    # from PyQt5.QtCore import pyqtSignal
    QT_VERSION = 5
except ImportError:
    try:
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QWidget, QProgressBar, \
            QPushButton, QTableWidget, QApplication, QHBoxLayout, QTableWidgetItem, QMessageBox

        QT_VERSION = 6
    except ImportError:
        print("错误: 未安装PyQt5或PyQt6")
        raise

from typing import Optional, Callable
from cfx_data_parser import ProgressCallback


class ProgressDialog(QDialog):
    """进度条对话框"""

    def __init__(self, title: str = "处理中...", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 120)

        layout = QVBoxLayout(self)

        self.label = QLabel("正在处理...")
        layout.addWidget(self.label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)

        self.detail_label = QLabel("")
        self.detail_label.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(self.detail_label)

        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        layout.addWidget(self.cancel_btn)

        self.cancelled = False

    def update_progress(self, value: Optional[int], text: str = "", detail: str = ""):
        """更新进度"""
        if value is not None:
            self.progress_bar.setValue(value)
        if text:
            self.label.setText(text)
        if detail:
            self.detail_label.setText(detail)
        QApplication.processEvents()

    def is_cancelled(self) -> bool:
        """检查是否被取消"""
        return self.cancelled

    def reject(self):
        """取消操作"""
        self.cancelled = True
        super().reject()


class DataInfoDialog(QDialog):
    """数据信息展示对话框"""

    def __init__(self, title: str, data_info: str, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(600, 400)

        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 创建表格
        self.table = QTableWidget()
        self.setup_table(data_info)
        layout.addWidget(self.table)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        layout.addLayout(button_layout)

    def setup_table(self, data_info: str):
        """设置表格数据"""
        # 解析数据信息
        regions_info = []
        if isinstance(data_info, str):
            # 解析字符串格式的数据信息
            sections = data_info.split('\n\n')
            for section in sections:
                if section.strip():
                    lines = section.strip().split('\n')
                    if lines:
                        region_name = lines[0].replace('区域 ', '').replace(':', '')
                        pressure_col = ""
                        pressure_range = ""
                        pressure_mean = ""

                        for line in lines[1:]:
                            if '压力列:' in line:
                                pressure_col = line.split('压力列:')[1].strip()
                            elif '范围:' in line:
                                pressure_range = line.split('范围:')[1].strip()
                            elif '平均值:' in line:
                                pressure_mean = line.split('平均值:')[1].strip()

                        regions_info.append({
                            'region': region_name,
                            'pressure_col': pressure_col,
                            'range': pressure_range,
                            'mean': pressure_mean
                        })

        # 设置表格
        if regions_info:
            self.table.setRowCount(len(regions_info))
            self.table.setColumnCount(4)
            self.table.setHorizontalHeaderLabels(['区域名称', '压力列名', '压力范围', '平均压力'])

            for i, info in enumerate(regions_info):
                self.table.setItem(i, 0, QTableWidgetItem(info['region']))
                self.table.setItem(i, 1, QTableWidgetItem(info['pressure_col']))
                self.table.setItem(i, 2, QTableWidgetItem(info['range']))
                self.table.setItem(i, 3, QTableWidgetItem(info['mean']))
        else:
            # 简单的信息显示
            self.table.setRowCount(1)
            self.table.setColumnCount(1)
            self.table.setHorizontalHeaderLabels(['信息'])
            self.table.setItem(0, 0, QTableWidgetItem(data_info))

        # 调整列宽
        self.table.resizeColumnsToContents()
        self.table.horizontalHeader().setStretchLastSection(True)


class ProgressManager:
    """进度管理器"""

    def __init__(self, parent: Optional[QWidget] = None):
        self.parent = parent
        self.current_dialog: Optional[ProgressDialog] = None

    def create_progress_dialog(self, title: str = "处理中...") -> ProgressDialog:
        """创建进度对话框"""
        self.current_dialog = ProgressDialog(title, self.parent)
        return self.current_dialog

    @staticmethod
    def create_progress_callback(dialog: ProgressDialog) -> ProgressCallback:
        """创建进度回调"""

        def callback_func(progress: int, message: str = "", detail: str = ""):
            if dialog and not dialog.is_cancelled():
                dialog.update_progress(progress, message, detail)

        callback = ProgressCallback(callback_func)
        # 添加取消检查方法
        callback.is_cancelled = lambda: dialog.is_cancelled() if dialog else False
        return callback

    def show_data_info(self, title: str, data_info: str):
        """显示数据信息对话框"""
        dialog = DataInfoDialog(title, data_info, self.parent)
        dialog.exec_()

    def show_progress_dialog(self, title: str, task_func: Callable, *args, **kwargs):
        """
        显示进度对话框并执行任务

        Args:
            title: 对话框标题
            task_func: 要执行的任务函数
            *args, **kwargs: 传递给任务函数的参数
        """
        dialog = self.create_progress_dialog(title)
        callback = self.create_progress_callback(dialog)

        dialog.show()
        QApplication.processEvents()  # 确保对话框显示

        try:
            # 执行任务，传入进度回调
            result = task_func(callback, *args, **kwargs)

            # 检查是否被取消
            if dialog.is_cancelled():
                dialog.close()
                print("操作已被用户取消")
                return None
            else:
                dialog.accept()
                return result

        except Exception as e:
            dialog.reject()
            error_msg = f"操作失败:\n{str(e)}"
            print(f"任务执行异常: {error_msg}")
            QMessageBox.critical(self.parent, "错误", error_msg)
            return None
        finally:
            # 确保对话框被关闭
            if dialog and dialog.isVisible():
                dialog.close()
            self.current_dialog = None

    def close_current_dialog(self):
        """关闭当前进度对话框"""
        if self.current_dialog:
            self.current_dialog.close()
            self.current_dialog = None

    def is_cancelled(self) -> bool:
        """检查当前操作是否被取消"""
        if self.current_dialog:
            return self.current_dialog.is_cancelled()
        return False


if __name__ == '__main__':
    import sys
    # 创建Qt应用
    app = QApplication(sys.argv)
    app.setApplicationName("progress_manager")
    app.setApplicationVersion("3.0")

    # 创建并显示主窗口
    window = QWidget()
    progress_manager = ProgressManager(window)

    def func():
        def load_task(callback):
            for i in range(10):
                time.sleep(1)
                if callback:
                    callback.update(i * 10 + 5, f"{i}/{10} ")
            return 'ok'
        success = progress_manager.show_progress_dialog("加载网格数据", load_task)

        print(success)
    btn = QPushButton(window)
    btn.setText("do something")
    btn.clicked.connect(func)
    window.show()
    sys.exit(app.exec_())
