#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VTK工厂类
统一创建和配置VTK对象，减少重复代码
"""

import vtk
import numpy as np
from typing import Optional, Tuple, Dict, Any, List
from utils.error_handler import handle_errors


class VTKFactory:
    """VTK对象工厂类 - 统一创建和配置VTK对象"""
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="VTK映射器创建")
    def create_mapper(input_connection=None, input_data=None, 
                     scalar_range: Optional[Tuple[float, float]] = None,
                     use_point_data: bool = True,
                     lookup_table: Optional[vtk.vtkLookupTable] = None) -> Optional[vtk.vtkPolyDataMapper]:
        """
        创建标准的VTK映射器
        
        Args:
            input_connection: 输入连接
            input_data: 输入数据
            scalar_range: 标量范围
            use_point_data: 是否使用点数据
            lookup_table: 颜色查找表
        
        Returns:
            配置好的映射器
        """
        mapper = vtk.vtkPolyDataMapper()
        
        # 设置输入
        if input_connection:
            mapper.SetInputConnection(input_connection)
        elif input_data:
            mapper.SetInputData(input_data)
        
        # 设置标量模式
        if use_point_data:
            mapper.SetScalarModeToUsePointData()
        else:
            mapper.SetScalarModeToUseCellData()
        
        # 设置标量范围
        if scalar_range:
            mapper.SetScalarRange(scalar_range[0], scalar_range[1])
        
        # 设置颜色查找表
        if lookup_table:
            mapper.SetLookupTable(lookup_table)
        
        return mapper
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="VTK演员创建")
    def create_actor(mapper: vtk.vtkMapper, 
                    color: Optional[Tuple[float, float, float]] = None,
                    opacity: float = 1.0,
                    line_width: int = 1,
                    point_size: int = 1,
                    representation: str = "surface") -> Optional[vtk.vtkActor]:
        """
        创建标准的VTK演员
        
        Args:
            mapper: 映射器
            color: 颜色 (r, g, b)
            opacity: 透明度
            line_width: 线宽
            point_size: 点大小
            representation: 表示方式 ("surface", "wireframe", "points")
        
        Returns:
            配置好的演员
        """
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        # 设置属性
        prop = actor.GetProperty()
        
        if color:
            prop.SetColor(color[0], color[1], color[2])
        
        prop.SetOpacity(opacity)
        prop.SetLineWidth(line_width)
        prop.SetPointSize(point_size)
        
        # 设置表示方式
        if representation == "wireframe":
            prop.SetRepresentationToWireframe()
        elif representation == "points":
            prop.SetRepresentationToPoints()
        else:
            prop.SetRepresentationToSurface()
        
        return actor
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="VTK流线追踪器创建")
    def create_streamline_tracer(input_data: vtk.vtkDataSet,
                                seed_source: vtk.vtkDataSet,
                                integration_direction: str = "both",
                                max_propagation: float = 1000.0,
                                initial_step_length: float = 0.01,
                                max_step_length: float = 0.1,
                                max_steps: int = 2000) -> Optional[vtk.vtkStreamTracer]:
        """
        创建流线追踪器
        
        Args:
            input_data: 输入数据
            seed_source: 种子源
            integration_direction: 积分方向 ("forward", "backward", "both")
            max_propagation: 最大传播距离
            initial_step_length: 初始步长
            max_step_length: 最大步长
            max_steps: 最大步数
        
        Returns:
            配置好的流线追踪器
        """
        tracer = vtk.vtkStreamTracer()
        tracer.SetInputData(input_data)
        tracer.SetSourceData(seed_source)
        
        # 设置积分方向
        if integration_direction == "forward":
            tracer.SetIntegrationDirectionToForward()
        elif integration_direction == "backward":
            tracer.SetIntegrationDirectionToBackward()
        else:
            tracer.SetIntegrationDirectionToBoth()
        
        # 设置积分参数
        tracer.SetMaximumPropagation(max_propagation)
        tracer.SetInitialIntegrationStep(initial_step_length)
        tracer.SetMaximumIntegrationStep(max_step_length)
        tracer.SetMaximumNumberOfSteps(max_steps)
        
        # 设置积分器类型
        tracer.SetIntegratorTypeToRungeKutta4()
        
        return tracer
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="VTK管道过滤器创建")
    def create_tube_filter(input_connection,
                          radius: float = 0.01,
                          number_of_sides: int = 8,
                          vary_radius: bool = False) -> Optional[vtk.vtkTubeFilter]:
        """
        创建管道过滤器
        
        Args:
            input_connection: 输入连接
            radius: 管道半径
            number_of_sides: 管道侧面数
            vary_radius: 是否变化半径
        
        Returns:
            配置好的管道过滤器
        """
        tube = vtk.vtkTubeFilter()
        tube.SetInputConnection(input_connection)
        tube.SetRadius(radius)
        tube.SetNumberOfSides(number_of_sides)
        
        if vary_radius:
            tube.SetVaryRadiusToVaryRadiusByVector()
        else:
            tube.SetVaryRadiusToVaryRadiusOff()
        
        return tube
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="VTK字形过滤器创建")
    def create_glyph_filter(input_connection,
                           source_connection,
                           scale_factor: float = 1.0,
                           scale_mode: str = "vector",
                           vector_mode: str = "use_vector") -> Optional[vtk.vtkGlyph3D]:
        """
        创建字形过滤器（用于矢量可视化）
        
        Args:
            input_connection: 输入连接
            source_connection: 源连接（如箭头）
            scale_factor: 缩放因子
            scale_mode: 缩放模式
            vector_mode: 矢量模式
        
        Returns:
            配置好的字形过滤器
        """
        glyph = vtk.vtkGlyph3D()
        glyph.SetInputConnection(input_connection)
        glyph.SetSourceConnection(source_connection)
        glyph.SetScaleFactor(scale_factor)
        
        # 设置缩放模式
        if scale_mode == "vector":
            glyph.SetScaleModeToScaleByVector()
        elif scale_mode == "scalar":
            glyph.SetScaleModeToScaleByScalar()
        else:
            glyph.SetScaleModeToDataScalingOff()
        
        # 设置矢量模式
        if vector_mode == "use_vector":
            glyph.SetVectorModeToUseVector()
        elif vector_mode == "use_normal":
            glyph.SetVectorModeToUseNormal()
        
        return glyph
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="VTK轮廓过滤器创建")
    def create_contour_filter(input_connection,
                             contour_values: List[float] = None,
                             contour_count: int = 10) -> Optional[vtk.vtkContourFilter]:
        """
        创建轮廓过滤器
        
        Args:
            input_connection: 输入连接
            contour_values: 轮廓值列表
            contour_count: 轮廓数量
        
        Returns:
            配置好的轮廓过滤器
        """
        contour = vtk.vtkContourFilter()
        contour.SetInputConnection(input_connection)
        
        if contour_values:
            for i, value in enumerate(contour_values):
                contour.SetValue(i, value)
        else:
            contour.GenerateValues(contour_count, 0.0, 1.0)
        
        return contour
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="VTK采样过滤器创建")
    def create_mask_points(input_data: vtk.vtkDataSet,
                          on_ratio: int = 1,
                          random_mode: bool = True,
                          max_points: Optional[int] = None) -> Optional[vtk.vtkMaskPoints]:
        """
        创建点采样过滤器
        
        Args:
            input_data: 输入数据
            on_ratio: 采样比率
            random_mode: 是否随机采样
            max_points: 最大点数
        
        Returns:
            配置好的采样过滤器
        """
        mask = vtk.vtkMaskPoints()
        mask.SetInputData(input_data)
        mask.SetOnRatio(on_ratio)
        
        if random_mode:
            mask.RandomModeOn()
        else:
            mask.RandomModeOff()
        
        if max_points:
            mask.SetMaximumNumberOfPoints(max_points)
        
        return mask


class VTKSourceFactory:
    """VTK源对象工厂类"""
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="箭头源创建")
    def create_arrow_source(tip_resolution: int = 6,
                           shaft_resolution: int = 6,
                           tip_radius: float = 0.1,
                           shaft_radius: float = 0.03,
                           tip_length: float = 0.35) -> Optional[vtk.vtkArrowSource]:
        """创建箭头源"""
        arrow = vtk.vtkArrowSource()
        arrow.SetTipResolution(tip_resolution)
        arrow.SetShaftResolution(shaft_resolution)
        arrow.SetTipRadius(tip_radius)
        arrow.SetShaftRadius(shaft_radius)
        arrow.SetTipLength(tip_length)
        return arrow
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="球体源创建")
    def create_sphere_source(radius: float = 0.5,
                            phi_resolution: int = 8,
                            theta_resolution: int = 8) -> Optional[vtk.vtkSphereSource]:
        """创建球体源"""
        sphere = vtk.vtkSphereSource()
        sphere.SetRadius(radius)
        sphere.SetPhiResolution(phi_resolution)
        sphere.SetThetaResolution(theta_resolution)
        return sphere
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="点源创建")
    def create_point_source(points: List[Tuple[float, float, float]]) -> Optional[vtk.vtkProgrammableSource]:
        """创建点源"""
        def create_points():
            output = source.GetPolyDataOutput()
            vtk_points = vtk.vtkPoints()
            for point in points:
                vtk_points.InsertNextPoint(point[0], point[1], point[2])
            output.SetPoints(vtk_points)
        
        source = vtk.vtkProgrammableSource()
        source.SetExecuteMethod(create_points)
        return source
