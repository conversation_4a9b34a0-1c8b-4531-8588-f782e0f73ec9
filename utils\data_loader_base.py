#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载基类
统一数据加载逻辑，减少重复代码
"""

import os
from typing import Dict, Optional, Callable, Any, List
from abc import ABC, abstractmethod
from utils.error_handler import handle_errors, ErrorContext


class ProgressCallback:
    """进度回调接口"""
    
    def __init__(self, callback: Optional[Callable[[int, str, str], None]] = None):
        self.callback = callback
        self.cancelled = False
        
    def update(self, progress: int, message: str = "", detail: str = ""):
        """更新进度"""
        if self.callback:
            self.callback(progress, message, detail)
            
    def is_cancelled(self) -> bool:
        """检查是否被取消"""
        return self.cancelled
        
    def cancel(self):
        """取消操作"""
        self.cancelled = True


class DataLoaderBase(ABC):
    """数据加载器基类"""
    
    def __init__(self):
        self.encoding_options = ['utf-8', 'latin-1', 'gbk', 'cp1252']
        self.supported_extensions = ['.csv', '.txt', '.dat']
        self.data_cache: Dict[str, Any] = {}
        self.load_history: List[str] = []
    
    @handle_errors(default_return=False, log_prefix="数据加载")
    def load_data(self, filename: str, 
                  data_type: str,
                  progress_callback: Optional[ProgressCallback] = None,
                  use_cache: bool = True) -> bool:
        """
        统一的数据加载方法
        
        Args:
            filename: 文件路径
            data_type: 数据类型标识
            progress_callback: 进度回调
            use_cache: 是否使用缓存
        
        Returns:
            是否加载成功
        """
        # 验证文件
        if not self._validate_file(filename):
            return False
        
        # 检查缓存
        cache_key = f"{data_type}:{filename}"
        if use_cache and cache_key in self.data_cache:
            print(f"✓ 使用缓存数据: {data_type}")
            return True
        
        if progress_callback:
            progress_callback.update(5, f"正在加载{data_type}数据...")
        
        try:
            # 执行具体的加载逻辑
            data = self._load_data_impl(filename, data_type, progress_callback)
            
            if data is not None:
                # 存储数据
                self._store_data(data_type, data)
                
                # 更新缓存
                if use_cache:
                    self.data_cache[cache_key] = data
                
                # 记录加载历史
                self.load_history.append(f"{data_type}:{filename}")
                
                if progress_callback:
                    progress_callback.update(100, f"{data_type}数据加载完成")
                
                print(f"✓ {data_type}数据加载成功: {self._get_data_summary(data)}")
                return True
            else:
                print(f"❌ {data_type}数据加载失败")
                return False
                
        except Exception as e:
            print(f"❌ {data_type}数据加载失败: {e}")
            return False
    
    def _validate_file(self, filename: str) -> bool:
        """验证文件有效性"""
        if not filename:
            print("❌ 文件路径为空")
            return False
        
        if not os.path.exists(filename):
            print(f"❌ 文件不存在: {filename}")
            return False
        
        if not os.path.isfile(filename):
            print(f"❌ 不是有效文件: {filename}")
            return False
        
        # 检查文件扩展名
        _, ext = os.path.splitext(filename.lower())
        if ext not in self.supported_extensions:
            print(f"⚠️ 不支持的文件扩展名: {ext}")
        
        # 检查文件大小
        file_size = os.path.getsize(filename)
        if file_size == 0:
            print(f"❌ 文件为空: {filename}")
            return False
        
        print(f"✓ 文件验证通过: {filename} ({file_size:,} bytes)")
        return True
    
    @handle_errors(default_return=None, log_prefix="文件读取")
    def read_file_with_encoding(self, filename: str) -> Optional[str]:
        """使用多种编码尝试读取文件"""
        content = None
        used_encoding = None
        
        for encoding in self.encoding_options:
            try:
                with open(filename, 'r', encoding=encoding) as f:
                    content = f.read()
                used_encoding = encoding
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文件失败 (编码: {encoding}): {e}")
                continue
        
        if content is None:
            print(f"❌ 无法读取文件，尝试了所有编码选项: {self.encoding_options}")
            return None
        
        print(f"✓ 文件读取成功，使用编码: {used_encoding}")
        return content
    
    @abstractmethod
    def _load_data_impl(self, filename: str, data_type: str, 
                       progress_callback: Optional[ProgressCallback] = None) -> Any:
        """具体的数据加载实现（子类必须实现）"""
        pass
    
    @abstractmethod
    def _store_data(self, data_type: str, data: Any):
        """存储数据（子类必须实现）"""
        pass
    
    @abstractmethod
    def _get_data_summary(self, data: Any) -> str:
        """获取数据摘要（子类必须实现）"""
        pass
    
    def clear_cache(self):
        """清空缓存"""
        self.data_cache.clear()
        print("✓ 数据缓存已清空")
    
    def clear_data(self, data_type: Optional[str] = None):
        """清空数据"""
        if data_type:
            # 清空特定类型的数据
            self._clear_specific_data(data_type)
            # 清空相关缓存
            keys_to_remove = [key for key in self.data_cache.keys() if key.startswith(f"{data_type}:")]
            for key in keys_to_remove:
                del self.data_cache[key]
            print(f"✓ {data_type}数据已清空")
        else:
            # 清空所有数据
            self._clear_all_data()
            self.data_cache.clear()
            print("✓ 所有数据已清空")
    
    @abstractmethod
    def _clear_specific_data(self, data_type: str):
        """清空特定类型的数据（子类必须实现）"""
        pass
    
    @abstractmethod
    def _clear_all_data(self):
        """清空所有数据（子类必须实现）"""
        pass
    
    def get_load_history(self) -> List[str]:
        """获取加载历史"""
        return self.load_history.copy()
    
    def has_data(self, data_type: str) -> bool:
        """检查是否有指定类型的数据"""
        return self._has_data_impl(data_type)
    
    @abstractmethod
    def _has_data_impl(self, data_type: str) -> bool:
        """检查数据实现（子类必须实现）"""
        pass
    
    def get_data_info(self, data_type: str) -> Dict[str, Any]:
        """获取数据信息"""
        return self._get_data_info_impl(data_type)
    
    @abstractmethod
    def _get_data_info_impl(self, data_type: str) -> Dict[str, Any]:
        """获取数据信息实现（子类必须实现）"""
        pass


class FileValidator:
    """文件验证工具"""
    
    @staticmethod
    @handle_errors(default_return=False, log_prefix="文件格式验证")
    def validate_csv_format(filename: str, 
                           required_headers: Optional[List[str]] = None,
                           min_rows: int = 1) -> bool:
        """
        验证CSV文件格式
        
        Args:
            filename: 文件路径
            required_headers: 必需的列头
            min_rows: 最小行数
        
        Returns:
            是否格式有效
        """
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < min_rows + 1:  # +1 for header
                print(f"❌ 文件行数不足: {len(lines)} < {min_rows + 1}")
                return False
            
            # 检查列头
            if required_headers:
                header_line = lines[0].strip()
                headers = [h.strip() for h in header_line.split(',')]
                
                missing_headers = [h for h in required_headers if h not in headers]
                if missing_headers:
                    print(f"❌ 缺少必需的列头: {missing_headers}")
                    return False
            
            print(f"✓ CSV格式验证通过: {len(lines)} 行")
            return True
            
        except Exception as e:
            print(f"❌ CSV格式验证失败: {e}")
            return False
    
    @staticmethod
    @handle_errors(default_return=False, log_prefix="数据块验证")
    def validate_data_blocks(content: str, 
                            block_markers: List[str] = None) -> bool:
        """
        验证数据块格式
        
        Args:
            content: 文件内容
            block_markers: 数据块标记
        
        Returns:
            是否格式有效
        """
        if not block_markers:
            block_markers = ['[Name]', '[Data]', '[Faces]']
        
        found_markers = []
        for marker in block_markers:
            if marker in content:
                found_markers.append(marker)
        
        if len(found_markers) < len(block_markers) // 2:  # 至少找到一半的标记
            print(f"❌ 数据块标记不足: 找到 {found_markers}, 期望 {block_markers}")
            return False
        
        print(f"✓ 数据块验证通过: 找到标记 {found_markers}")
        return True
