#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFX数据模型类
定义数据结构和基础数据操作
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass


@dataclass
class CoordinateColumns:
    """坐标列信息"""
    x_col: str
    y_col: str  
    z_col: str


@dataclass
class PhysicalColumns:
    """物理量列信息"""
    pressure_col: Optional[str] = None
    velocity_u_col: Optional[str] = None
    velocity_v_col: Optional[str] = None
    velocity_w_col: Optional[str] = None


class RegionData:
    """区域数据模型类"""
    
    def __init__(self, name: str, data: pd.DataFrame):
        """
        初始化区域数据
        
        Args:
            name: 区域名称
            data: 数据DataFrame
        """
        self.name = name
        self.data = data
        self.faces: Optional[List[List[int]]] = None
        self._coordinate_columns: Optional[CoordinateColumns] = None
        self._physical_columns: Optional[PhysicalColumns] = None
        
    def set_faces(self, faces: List[List[int]]):
        """设置面片数据"""
        self.faces = faces
        
    def has_faces(self) -> bool:
        """检查是否有面片数据"""
        return self.faces is not None and len(self.faces) > 0
        
    def get_coordinate_columns(self) -> CoordinateColumns:
        """获取坐标列信息"""
        if self._coordinate_columns is None:
            self._coordinate_columns = self._find_coordinate_columns()
        return self._coordinate_columns
        
    def get_physical_columns(self) -> PhysicalColumns:
        """获取物理量列信息"""
        if self._physical_columns is None:
            self._physical_columns = self._find_physical_columns()
        return self._physical_columns
        
    def _find_coordinate_columns(self) -> CoordinateColumns:
        """查找坐标列"""
        columns = self.data.columns.tolist()
        x_col = y_col = z_col = None
        
        # 查找X坐标列
        for col in columns:
            if 'X' in col and ('m' in col or 'M' in col):
                x_col = col
                break
                
        # 查找Y坐标列
        for col in columns:
            if 'Y' in col and ('m' in col or 'M' in col):
                y_col = col
                break
                
        # 查找Z坐标列
        for col in columns:
            if 'Z' in col and ('m' in col or 'M' in col):
                z_col = col
                break
                
        if not all([x_col, y_col, z_col]):
            raise ValueError(f"区域 {self.name} 未找到完整的坐标列。找到: X={x_col}, Y={y_col}, Z={z_col}")
            
        return CoordinateColumns(x_col, y_col, z_col)
        
    def _find_physical_columns(self) -> PhysicalColumns:
        """查找物理量列"""
        columns = self.data.columns.tolist()

        # 查找压力列
        pressure_col = None
        for col in columns:
            if 'Pressure' in col and '[ Pa ]' in col and 'Gradient' not in col and 'Total' not in col:
                pressure_col = col
                break

        # 查找速度分量列
        velocity_u_col = velocity_v_col = velocity_w_col = None

        # 查找u分量
        for col in columns:
            if 'Velocity u' in col or 'Velocity U' in col:
                velocity_u_col = col
                break

        # 查找v分量
        for col in columns:
            if 'Velocity v' in col or 'Velocity V' in col:
                velocity_v_col = col
                break

        # 查找w分量
        for col in columns:
            if 'Velocity w' in col or 'Velocity W' in col:
                velocity_w_col = col
                break

        # print(f"区域 {self.name}: 找到速度列 u={velocity_u_col}, v={velocity_v_col}, w={velocity_w_col}")

        return PhysicalColumns(
            pressure_col=pressure_col,
            velocity_u_col=velocity_u_col,
            velocity_v_col=velocity_v_col,
            velocity_w_col=velocity_w_col
        )

    def get_available_physical_fields(self) -> Dict[str, str]:
        """获取所有可用的物理场及其列名"""
        columns = self.data.columns.tolist()
        fields = {}

        # 压力场
        for col in columns:
            if 'Pressure' in col and '[ Pa ]' in col and 'Gradient' not in col and 'Total' not in col:
                fields['pressure'] = col
                break

        # 温度场
        for col in columns:
            if 'Temperature' in col and '[ K ]' in col:
                fields['temperature'] = col
                break

        # 密度场
        for col in columns:
            if 'Density' in col and '[ kg m^-3 ]' in col:
                fields['density'] = col
                break

        # 湍流动能
        for col in columns:
            if 'Turbulence Kinetic Energy' in col:
                fields['turbulence_kinetic_energy'] = col
                break

        # 湍流耗散率
        for col in columns:
            if 'Turbulence Eddy Dissipation' in col:
                fields['turbulence_dissipation'] = col
                break

        # 速度幅度
        for col in columns:
            if 'Velocity' in col and 'Magnitude' in col:
                fields['velocity_magnitude'] = col
                break

        return fields

    def get_physical_field_data(self, field_name: str) -> Optional[np.ndarray]:
        """获取指定物理场的数据"""
        available_fields = self.get_available_physical_fields()
        if field_name in available_fields:
            return self.data[available_fields[field_name]].values
        return None
        
    def get_coordinates(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """获取坐标数据"""
        coord_cols = self.get_coordinate_columns()
        return (
            self.data[coord_cols.x_col].values,
            self.data[coord_cols.y_col].values,
            self.data[coord_cols.z_col].values
        )
        
    def get_pressure_data(self) -> Optional[np.ndarray]:
        """获取压力数据"""
        phys_cols = self.get_physical_columns()
        if phys_cols.pressure_col:
            return self.data[phys_cols.pressure_col].values
        return None
        
    def get_velocity_data(self) -> Optional[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
        """获取速度矢量数据"""
        phys_cols = self.get_physical_columns()
        if all([phys_cols.velocity_u_col, phys_cols.velocity_v_col, phys_cols.velocity_w_col]):
            return (
                self.data[phys_cols.velocity_u_col].values,
                self.data[phys_cols.velocity_v_col].values,
                self.data[phys_cols.velocity_w_col].values
            )
        return None
        
    def get_bounds(self) -> Tuple[float, float, float, float, float, float]:
        """获取数据边界 [xmin, xmax, ymin, ymax, zmin, zmax]"""
        x, y, z = self.get_coordinates()
        return (x.min(), x.max(), y.min(), y.max(), z.min(), z.max())
        
    def sample_data(self, sampling_rate: int) -> 'RegionData':
        """数据采样"""
        if sampling_rate <= 1:
            return self
            
        sampled_data = self.data.iloc[::sampling_rate].copy()
        sampled_region = RegionData(f"{self.name}_sampled", sampled_data)
        
        # 如果有面片数据，也需要相应调整（这里简化处理）
        if self.has_faces():
            # 面片索引需要重新映射，这里暂时不处理
            pass
            
        return sampled_region
        
    def get_info(self) -> Dict[str, Any]:
        """获取区域信息摘要"""
        info = {
            'name': self.name,
            'point_count': len(self.data),
            'has_faces': self.has_faces(),
            'face_count': len(self.faces) if self.has_faces() else 0
        }
        
        # 添加物理量信息
        phys_cols = self.get_physical_columns()
        if phys_cols.pressure_col:
            pressure_data = self.get_pressure_data()
            info['pressure'] = {
                'column': phys_cols.pressure_col,
                'min': float(pressure_data.min()),
                'max': float(pressure_data.max()),
                'mean': float(pressure_data.mean())
            }
            
        if phys_cols.velocity_u_col:
            info['has_velocity'] = True
            info['velocity_columns'] = [
                phys_cols.velocity_u_col,
                phys_cols.velocity_v_col, 
                phys_cols.velocity_w_col
            ]
        else:
            info['has_velocity'] = False
            
        return info
        
    def __len__(self) -> int:
        """返回数据点数量"""
        return len(self.data)
        
    def __str__(self) -> str:
        """字符串表示"""
        return f"RegionData(name='{self.name}', points={len(self.data)}, faces={len(self.faces) if self.has_faces() else 0})"
