#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VTK渲染器基础类
提供VTK渲染的基础功能和工具方法
"""

import vtk
import numpy as np
from vtk.util import numpy_support
from typing import Optional, Tuple, List
from data_models import RegionData


class VTKRenderer:
    """VTK渲染器基础类"""
    
    def __init__(self):
        self.renderer = None
        self.render_window = None
        self.interactor = None
        
    def setup_renderer(self, renderer: vtk.vtkRenderer, render_window: vtk.vtkRenderWindow, 
                      interactor: vtk.vtkRenderWindowInteractor):
        """设置VTK渲染组件"""
        self.renderer = renderer
        self.render_window = render_window
        self.interactor = interactor
        
    def create_vtk_grid(self, region_data: RegionData, sampling_rate: int = 1) -> vtk.vtkUnstructuredGrid:
        """
        为区域数据创建VTK非结构化网格
        
        Args:
            region_data: 区域数据
            sampling_rate: 采样率
            
        Returns:
            vtk.vtkUnstructuredGrid: VTK网格对象
        """
        # 获取坐标数据
        x, y, z = region_data.get_coordinates()
        
        # 数据采样
        if sampling_rate > 1:
            indices = np.arange(0, len(x), sampling_rate)
            x = x[indices]
            y = y[indices]
            z = z[indices]
            sampled_data = region_data.data.iloc[indices]
        else:
            sampled_data = region_data.data
            
        # 创建VTK点
        points = vtk.vtkPoints()
        for i in range(len(x)):
            points.InsertNextPoint(x[i], y[i], z[i])
            
        # 创建非结构化网格
        ugrid = vtk.vtkUnstructuredGrid()
        ugrid.SetPoints(points)
        
        # 使用面片信息创建面单元
        faces = region_data.faces
        if faces is not None and len(faces) > 0:
            print(f"区域 {region_data.name}: 使用 {len(faces)} 个面片创建网格单元")

            # 创建面单元
            cells_created = 0
            for face in faces:
                # 检查面片索引是否有效
                if all(0 <= idx < len(x) for idx in face):
                    if len(face) == 3:  # 三角形面
                        cell = vtk.vtkTriangle()
                        for j in range(3):
                            cell.GetPointIds().SetId(j, face[j])
                        ugrid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())
                        cells_created += 1
                    elif len(face) == 4:  # 四边形面
                        cell = vtk.vtkQuad()
                        for j in range(4):
                            cell.GetPointIds().SetId(j, face[j])
                        ugrid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())
                        cells_created += 1

            print(f"区域 {region_data.name}: 创建了 {cells_created} 个面单元")
        else:
            print(f"区域 {region_data.name}: 没有面片信息，使用点单元")
            # 如果没有面片信息，回退到点单元
            for i in range(len(x)):
                cell = vtk.vtkVertex()
                cell.GetPointIds().SetId(0, i)
                ugrid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())
            
        # 添加标量数据（压力）
        pressure_data = region_data.get_pressure_data()
        if pressure_data is not None:
            if sampling_rate > 1:
                pressure_data = pressure_data[indices]
            scalar_array = numpy_support.numpy_to_vtk(pressure_data)
            scalar_array.SetName("Pressure")
            ugrid.GetPointData().AddArray(scalar_array)
            ugrid.GetPointData().SetActiveScalars("Pressure")
            
        # 添加矢量数据（速度）
        velocity_data = region_data.get_velocity_data()
        if velocity_data is not None:
            u, v, w = velocity_data
            if sampling_rate > 1:
                u = u[indices]
                v = v[indices]
                w = w[indices]
            vectors = np.column_stack([u, v, w])

            # 检查速度数据范围
            velocity_magnitude = np.sqrt(u**2 + v**2 + w**2)
            print(f"区域 {region_data.name}: 速度幅度范围 {velocity_magnitude.min():.6f} ~ {velocity_magnitude.max():.6f} m/s")

            vector_array = numpy_support.numpy_to_vtk(vectors)
            vector_array.SetName("Velocity")
            ugrid.GetPointData().AddArray(vector_array)
            ugrid.GetPointData().SetActiveVectors("Velocity")
            print(f"区域 {region_data.name}: 矢量数据已添加到VTK网格")
        else:
            print(f"区域 {region_data.name}: 没有速度数据")
            
        return ugrid
        
    def create_rainbow_colormap(self) -> vtk.vtkLookupTable:
        """创建彩虹色颜色映射（红->紫，从高到低）"""
        lut = vtk.vtkLookupTable()
        lut.SetNumberOfTableValues(256)
        lut.SetRange(0.0, 1.0)
        
        # 设置彩虹色：从红色到紫色
        for i in range(256):
            ratio = i / 255.0
            
            # 使用HSV色彩空间创建彩虹色
            # 色相从0（红色）到270度（紫色）
            hue = (1.0 - ratio) * 270.0 / 360.0  # 反向：高值=红色，低值=紫色
            saturation = 1.0  # 饱和度
            value = 1.0       # 亮度
            
            # HSV转RGB
            import colorsys
            r, g, b = colorsys.hsv_to_rgb(hue, saturation, value)
            
            lut.SetTableValue(i, r, g, b, 1.0)
            
        lut.Build()
        return lut
        
    def create_axes_actor(self, bounds: Optional[Tuple[float, float, float, float, float, float]] = None) -> vtk.vtkAxesActor:
        """创建坐标轴actor"""
        axes = vtk.vtkAxesActor()
        
        # 设置坐标轴长度
        if bounds:
            axis_length = min(bounds[1] - bounds[0],
                            bounds[3] - bounds[2],
                            bounds[5] - bounds[4]) / 10
            axes.SetTotalLength(axis_length, axis_length, axis_length)
        else:
            axes.SetTotalLength(0.1, 0.1, 0.1)  # 默认大小
            
        # 设置文本属性
        # X轴标签
        x_caption = axes.GetXAxisCaptionActor2D()
        x_caption.GetTextActor().SetTextScaleModeToNone()
        x_caption.GetTextActor().GetTextProperty().SetFontSize(12)
        x_caption.GetTextActor().GetTextProperty().SetColor(1, 0, 0)  # 红色
        
        # Y轴标签
        y_caption = axes.GetYAxisCaptionActor2D()
        y_caption.GetTextActor().SetTextScaleModeToNone()
        y_caption.GetTextActor().GetTextProperty().SetFontSize(12)
        y_caption.GetTextActor().GetTextProperty().SetColor(0, 1, 0)  # 绿色
        
        # Z轴标签
        z_caption = axes.GetZAxisCaptionActor2D()
        z_caption.GetTextActor().SetTextScaleModeToNone()
        z_caption.GetTextActor().GetTextProperty().SetFontSize(12)
        z_caption.GetTextActor().GetTextProperty().SetColor(0, 0, 1)  # 蓝色
        
        return axes
        
    def create_scalar_bar(self, lookup_table: vtk.vtkLookupTable, title: str = "Pressure [Pa]",
                         position: Tuple[float, float] = (0.85, 0.1)) -> vtk.vtkScalarBarActor:
        """创建颜色条"""
        scalar_bar = vtk.vtkScalarBarActor()
        scalar_bar.SetLookupTable(lookup_table)
        scalar_bar.SetTitle(title)
        scalar_bar.SetNumberOfLabels(5)
        scalar_bar.SetPosition(position[0], position[1])
        scalar_bar.SetWidth(0.1)
        scalar_bar.SetHeight(0.8)
        return scalar_bar
        
    def sample_ugrid_for_performance(self, ugrid: vtk.vtkUnstructuredGrid, target_points: int) -> vtk.vtkUnstructuredGrid:
        """为提高性能对网格进行采样"""
        num_points = ugrid.GetNumberOfPoints()
        if num_points <= target_points:
            return ugrid
            
        # 计算采样比率
        ratio = target_points / num_points
        
        # 使用VTK的随机采样
        mask = vtk.vtkMaskPoints()
        mask.SetInputData(ugrid)
        mask.SetOnRatio(max(1, int(1.0 / ratio)))
        mask.RandomModeOn()
        mask.Update()
        
        # 转换回非结构化网格
        sampled_points = mask.GetOutput()
        if sampled_points.GetNumberOfPoints() == 0:
            return ugrid
            
        # 创建新的非结构化网格
        new_ugrid = vtk.vtkUnstructuredGrid()
        new_ugrid.SetPoints(sampled_points.GetPoints())
        
        # 复制点数据
        new_ugrid.GetPointData().ShallowCopy(sampled_points.GetPointData())
        
        # 添加顶点单元
        for i in range(sampled_points.GetNumberOfPoints()):
            cell = vtk.vtkVertex()
            cell.GetPointIds().SetId(0, i)
            new_ugrid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())
            
        return new_ugrid
        
    def add_actor(self, actor: vtk.vtkActor):
        """添加actor到渲染器"""
        if self.renderer and actor:
            self.renderer.AddActor(actor)
            
    def add_actor_2d(self, actor: vtk.vtkActor2D):
        """添加2D actor到渲染器"""
        if self.renderer and actor:
            self.renderer.AddActor2D(actor)
            
    def remove_actor(self, actor: vtk.vtkActor):
        """从渲染器移除actor"""
        if self.renderer and actor:
            self.renderer.RemoveActor(actor)
            
    def remove_all_actors(self):
        """移除所有actors"""
        if self.renderer:
            self.renderer.RemoveAllViewProps()
            
    def reset_camera(self):
        """重置相机"""
        if self.renderer:
            self.renderer.ResetCamera()
            
    def render(self):
        """执行渲染"""
        if self.render_window:
            self.render_window.Render()
            
    def set_background(self, r: float, g: float, b: float):
        """设置背景颜色"""
        if self.renderer:
            self.renderer.SetBackground(r, g, b)
