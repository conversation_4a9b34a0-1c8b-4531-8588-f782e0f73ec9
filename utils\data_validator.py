#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证工具
统一处理各种数据的有效性检查
"""

import vtk
import numpy as np
from typing import Optional, Tuple, Dict, Any, List
from utils.error_handler import handle_errors, ErrorContext


class DataValidator:
    """数据验证器 - 统一数据有效性检查"""
    
    @staticmethod
    @handle_errors(default_return=False, log_prefix="VTK数据验证")
    def validate_vtk_grid(ugrid: vtk.vtkUnstructuredGrid, 
                         check_scalars: bool = True,
                         check_vectors: bool = True,
                         min_points: int = 1) -> bool:
        """
        验证VTK网格数据的有效性
        
        Args:
            ugrid: VTK非结构化网格
            check_scalars: 是否检查标量数据
            check_vectors: 是否检查矢量数据
            min_points: 最小点数要求
        
        Returns:
            是否有效
        """
        if not ugrid:
            print("❌ VTK网格为空")
            return False
        
        # 检查点数
        num_points = ugrid.GetNumberOfPoints()
        if num_points < min_points:
            print(f"❌ 点数不足: {num_points} < {min_points}")
            return False
        
        print(f"✓ VTK网格点数: {num_points}")
        
        # 检查标量数据
        if check_scalars:
            scalars = ugrid.GetPointData().GetScalars()
            if scalars is None:
                print("⚠️ 没有标量数据")
                return False
            else:
                scalar_range = scalars.GetRange()
                print(f"✓ 标量数据范围: {scalar_range[0]:.6f} ~ {scalar_range[1]:.6f}")
        
        # 检查矢量数据
        if check_vectors:
            vectors = ugrid.GetPointData().GetVectors()
            if vectors is None:
                print("⚠️ 没有矢量数据")
                return False
            else:
                vector_range = vectors.GetRange(-1)
                print(f"✓ 矢量数据范围: {vector_range[0]:.6f} ~ {vector_range[1]:.6f}")
        
        return True
    
    @staticmethod
    @handle_errors(default_return=False, log_prefix="矢量数据质量检查")
    def validate_vector_quality(ugrid: vtk.vtkUnstructuredGrid,
                               sample_size: int = 100,
                               min_valid_ratio: float = 0.8) -> bool:
        """
        检查矢量数据质量
        
        Args:
            ugrid: VTK网格
            sample_size: 采样大小
            min_valid_ratio: 最小有效比例
        
        Returns:
            矢量数据是否质量良好
        """
        vectors = ugrid.GetPointData().GetVectors()
        if not vectors:
            return False
        
        num_points = min(sample_size, ugrid.GetNumberOfPoints())
        num_valid = 0
        num_zero = 0
        
        for i in range(num_points):
            vec = vectors.GetTuple3(i)
            magnitude = (vec[0]**2 + vec[1]**2 + vec[2]**2)**0.5
            
            # 检查是否为有效数值
            if not (magnitude != magnitude or magnitude == float('inf')):  # 不是NaN或inf
                num_valid += 1
                if magnitude < 1e-10:
                    num_zero += 1
        
        valid_ratio = num_valid / num_points
        zero_ratio = num_zero / num_points if num_valid > 0 else 1.0
        
        print(f"矢量质量检查: {num_valid}/{num_points} 有效 ({valid_ratio:.2%}), "
              f"{num_zero} 接近零 ({zero_ratio:.2%})")
        
        return valid_ratio >= min_valid_ratio
    
    @staticmethod
    @handle_errors(default_return=False, log_prefix="标量数据质量检查")
    def validate_scalar_quality(ugrid: vtk.vtkUnstructuredGrid,
                               sample_size: int = 100,
                               min_valid_ratio: float = 0.9) -> bool:
        """
        检查标量数据质量
        
        Args:
            ugrid: VTK网格
            sample_size: 采样大小
            min_valid_ratio: 最小有效比例
        
        Returns:
            标量数据是否质量良好
        """
        scalars = ugrid.GetPointData().GetScalars()
        if not scalars:
            return False
        
        num_points = min(sample_size, ugrid.GetNumberOfPoints())
        num_valid = 0
        
        for i in range(num_points):
            value = scalars.GetValue(i)
            
            # 检查是否为有效数值
            if not (value != value or value == float('inf') or value == float('-inf')):
                num_valid += 1
        
        valid_ratio = num_valid / num_points
        print(f"标量质量检查: {num_valid}/{num_points} 有效 ({valid_ratio:.2%})")
        
        return valid_ratio >= min_valid_ratio
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="数据范围验证")
    def validate_data_range(data_array, 
                           expected_min: Optional[float] = None,
                           expected_max: Optional[float] = None,
                           allow_zero: bool = True) -> Optional[Tuple[float, float]]:
        """
        验证数据范围的合理性
        
        Args:
            data_array: 数据数组
            expected_min: 期望的最小值
            expected_max: 期望的最大值
            allow_zero: 是否允许零值
        
        Returns:
            有效的数据范围或None
        """
        if data_array is None:
            return None
        
        # 获取数据范围
        if hasattr(data_array, 'GetRange'):
            data_range = data_array.GetRange()
        elif hasattr(data_array, 'min') and hasattr(data_array, 'max'):
            data_range = (float(data_array.min()), float(data_array.max()))
        else:
            return None
        
        min_val, max_val = data_range
        
        # 检查是否有无效值
        if min_val != min_val or max_val != max_val:  # NaN检查
            print("❌ 数据包含NaN值")
            return None
        
        if min_val == float('-inf') or max_val == float('inf'):
            print("❌ 数据包含无穷大值")
            return None
        
        # 检查范围合理性
        if min_val > max_val:
            print(f"❌ 数据范围无效: min({min_val}) > max({max_val})")
            return None
        
        # 检查期望范围
        if expected_min is not None and min_val < expected_min:
            print(f"⚠️ 最小值 {min_val} 小于期望值 {expected_min}")
        
        if expected_max is not None and max_val > expected_max:
            print(f"⚠️ 最大值 {max_val} 大于期望值 {expected_max}")
        
        # 检查零值
        if not allow_zero and min_val <= 0:
            print(f"⚠️ 数据包含零或负值: min={min_val}")
        
        print(f"✓ 数据范围有效: {min_val:.6f} ~ {max_val:.6f}")
        return data_range
    
    @staticmethod
    @handle_errors(default_return=False, log_prefix="边界验证")
    def validate_bounds(bounds: Tuple[float, float, float, float, float, float],
                       min_size: float = 1e-6) -> bool:
        """
        验证边界的有效性
        
        Args:
            bounds: 边界 (xmin, xmax, ymin, ymax, zmin, zmax)
            min_size: 最小尺寸要求
        
        Returns:
            边界是否有效
        """
        if not bounds or len(bounds) != 6:
            print("❌ 边界格式无效")
            return False
        
        xmin, xmax, ymin, ymax, zmin, zmax = bounds
        
        # 检查是否有无效值
        for val in bounds:
            if val != val or abs(val) == float('inf'):  # NaN或inf检查
                print(f"❌ 边界包含无效值: {val}")
                return False
        
        # 检查边界顺序
        if xmin > xmax or ymin > ymax or zmin > zmax:
            print(f"❌ 边界顺序错误: x[{xmin}, {xmax}], y[{ymin}, {ymax}], z[{zmin}, {zmax}]")
            return False
        
        # 检查边界尺寸
        x_size = xmax - xmin
        y_size = ymax - ymin
        z_size = zmax - zmin
        
        if x_size < min_size or y_size < min_size or z_size < min_size:
            print(f"❌ 边界尺寸过小: {x_size:.6f} x {y_size:.6f} x {z_size:.6f}")
            return False
        
        print(f"✓ 边界有效: [{xmin:.3f}, {xmax:.3f}] x [{ymin:.3f}, {ymax:.3f}] x [{zmin:.3f}, {zmax:.3f}]")
        return True
    
    @staticmethod
    @handle_errors(default_return={}, log_prefix="数据完整性检查")
    def check_data_completeness(region_data) -> Dict[str, bool]:
        """
        检查区域数据的完整性
        
        Args:
            region_data: 区域数据对象
        
        Returns:
            完整性检查结果字典
        """
        results = {
            'has_coordinates': False,
            'has_pressure': False,
            'has_velocity': False,
            'has_faces': False,
            'coordinates_valid': False,
            'pressure_valid': False,
            'velocity_valid': False
        }
        
        # 检查坐标数据
        try:
            x, y, z = region_data.get_coordinates()
            results['has_coordinates'] = True
            if len(x) > 0 and len(y) > 0 and len(z) > 0:
                results['coordinates_valid'] = True
        except:
            pass
        
        # 检查压力数据
        try:
            pressure = region_data.get_pressure_data()
            if pressure is not None and len(pressure) > 0:
                results['has_pressure'] = True
                if not np.any(np.isnan(pressure)) and not np.any(np.isinf(pressure)):
                    results['pressure_valid'] = True
        except:
            pass
        
        # 检查速度数据
        try:
            velocity = region_data.get_velocity_data()
            if velocity is not None:
                u, v, w = velocity
                if len(u) > 0 and len(v) > 0 and len(w) > 0:
                    results['has_velocity'] = True
                    if (not np.any(np.isnan(u)) and not np.any(np.isnan(v)) and not np.any(np.isnan(w)) and
                        not np.any(np.isinf(u)) and not np.any(np.isinf(v)) and not np.any(np.isinf(w))):
                        results['velocity_valid'] = True
        except:
            pass
        
        # 检查面片数据
        try:
            results['has_faces'] = region_data.has_faces()
        except:
            pass
        
        return results
