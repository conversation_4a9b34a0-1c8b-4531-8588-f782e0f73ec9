[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cfx-postprocessing"
version = "3.0.0"
description = "CFX流体仿真结果可视化工具 - 基于VTK的交互式3D可视化"
readme = "README_重构版.md"
license = {text = "MIT"}
authors = [
    {name = "CFX Visualization Team"},
]
maintainers = [
    {name = "CFX Visualization Team"},
]
keywords = [
    "CFX", "VTK", "visualization", "fluid", "simulation", 
    "postprocessing", "3D", "streamlines", "pressure"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Visualization",
    "Topic :: Scientific/Engineering :: Physics",
    "Topic :: Multimedia :: Graphics :: 3D Rendering",
]
requires-python = ">=3.7"
dependencies = [
    "numpy>=1.19.0",
    "pandas>=1.3.0",
    "vtk>=9.0.0",
]

[project.optional-dependencies]
# GUI界面依赖 (二选一)
qt5 = [
    "PyQt5>=5.15.0",
]
qt6 = [
    "PyQt6>=6.0.0",
]
# 可选的科学计算依赖
scientific = [
    "matplotlib>=3.3.0",
    "scipy>=1.7.0",
]
# 开发依赖
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.10.0",
    "black>=21.0.0",
    "flake8>=3.8.0",
    "mypy>=0.800",
]
# 完整安装 (推荐PyQt5)
full = [
    "PyQt5>=5.15.0",
    "matplotlib>=3.3.0",
    "scipy>=1.7.0",
]
# 所有依赖
all = [
    "PyQt5>=5.15.0",
#    "PyQt6>=6.0.0",
    "matplotlib>=3.3.0",
    "scipy>=1.7.0",
    "pytest>=6.0.0",
    "pytest-cov>=2.10.0",
    "black>=21.0.0",
    "flake8>=3.8.0",
    "mypy>=0.800",
]

[project.urls]
Homepage = "https://github.com/your-org/cfx-postprocessing"
Documentation = "https://github.com/your-org/cfx-postprocessing/blob/main/README_重构版.md"
Repository = "https://github.com/your-org/cfx-postprocessing.git"
"Bug Tracker" = "https://github.com/your-org/cfx-postprocessing/issues"

[project.scripts]
cfx-visualizer = "main_window:main"
cfx-quick-viz = "quick_visualize:main"

[project.gui-scripts]
cfx-gui = "main_window:main"

[tool.setuptools]
packages = [
    "cfx_postprocessing",
]

[tool.setuptools.package-dir]
cfx_postprocessing = "."

[tool.setuptools.package-data]
cfx_postprocessing = [
    "demo_data/**/*.csv",
    "*.md",
]

# 代码质量工具配置
[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除目录
  __pycache__
  | \.git
  | \.mypy_cache
  | \.pytest_cache
  | \.venv
  | venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    "__pycache__",
    ".git",
    ".mypy_cache",
    ".pytest_cache",
    ".venv",
    "venv",
    "build",
    "dist",
]

#[tool.mypy]
##python_version = "3.7"
#warn_return_any = true
#warn_unused_configs = true
#disallow_untyped_defs = true
#disallow_incomplete_defs = true
#check_untyped_defs = true
#disallow_untyped_decorators = true
#no_implicit_optional = true
#warn_redundant_casts = true
#warn_unused_ignores = true
#warn_no_return = true
#warn_unreachable = true
#strict_equality = true

[[tool.mypy.overrides]]
module = [
    "vtk.*",
    "PyQt5.*",
    "PyQt6.*",
]
ignore_missing_imports = true
follow_untyped_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=cfx_postprocessing",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["cfx_postprocessing"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/demo_data/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
