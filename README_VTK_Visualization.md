# CFX VTK 流体可视化工具

基于VTK的CFX仿真结果可视化工具，支持流体流速迹线云图和压力分布云图的交互式可视化。

## 功能特性

### 核心功能
- **压力分布云图**: 3D压力场可视化，支持颜色映射
- **流速迹线云图**: 基于速度矢量场的流线可视化
- **速度矢量显示**: 3D箭头显示速度方向和大小
- **交互式3D查看**: 支持旋转、缩放、平移操作

### 数据支持
- CFX导出的CSV格式文件
- 稳态和瞬态仿真结果
- 自动解析网格坐标和物理量

### 可视化控制
- 实时调整流线密度
- 速度矢量缩放控制
- 显示选项开关
- 颜色条和坐标轴显示

## 文件结构

```
├── vtk_flow_visualizer.py     # 完整GUI可视化工具
├── quick_visualize.py         # 快速可视化脚本
├── install_dependencies.py    # 依赖安装脚本
├── README_VTK_Visualization.md # 使用说明
└── demo_data/
    └── case1/
        ├── mesh.csv           # 网格数据
        ├── steady_results.csv # 稳态结果
        └── transient_results.csv # 瞬态结果
```

## 安装和使用

### 1. 安装依赖

```bash
# 自动检查和安装依赖
python install_dependencies.py

# 或手动安装
pip install numpy pandas vtk
```

### 2. 快速开始

```bash
# 快速可视化demo数据
python quick_visualize.py
```

### 3. 完整GUI版本

```bash
# 启动完整的GUI界面
python vtk_flow_visualizer.py
```

## 使用指南

### GUI界面操作

1. **数据加载**
   - 点击"加载网格数据"选择mesh.csv
   - 点击"加载稳态结果"选择steady_results.csv
   - 点击"加载瞬态结果"选择transient_results.csv

2. **可视化设置**
   - 选择数据源：稳态或瞬态
   - 勾选显示选项：压力分布、流线、速度矢量
   - 调整参数：流线密度、矢量缩放

3. **生成和控制**
   - 点击"生成可视化"创建3D场景
   - 点击"更新显示"应用新设置
   - 使用鼠标在3D窗口中交互

4. **导出功能**
   - "保存图像"：导出PNG/JPEG格式
   - "导出VTK"：保存VTK格式用于其他软件

### 快速脚本使用

```python
# 修改quick_visualize.py中的参数
quick_visualize(
    data_dir="your_data_directory",  # 数据目录
    use_steady=True                  # True=稳态, False=瞬态
)
```

## 数据格式要求

### CFX CSV文件格式
```
[Name]
数据集名称

[Data]
X [ m ], Y [ m ], Z [ m ], Pressure [ Pa ], Velocity u [ m s^-1 ], ...
数据行1
数据行2
...
```

### 必需的数据列
- **坐标**: X, Y, Z (支持不同单位标记)
- **压力**: Pressure (任何包含"Pressure"的列)
- **速度**: Velocity u, Velocity v, Velocity w

## 技术特性

### VTK可视化技术
- 非结构化网格(vtkUnstructuredGrid)
- 流线追踪(vtkStreamTracer)
- 标量/矢量数据映射
- 交互式渲染

### 性能优化
- 点云可视化适合大数据集
- 可调节流线密度控制计算量
- 内存高效的数据处理

### 扩展性
- 模块化设计便于功能扩展
- 支持多种数据格式
- 可集成到其他应用中

## 故障排除

### 常见问题

1. **VTK导入错误**
   ```bash
   pip install vtk
   # 或使用conda
   conda install vtk
   ```

2. **数据文件格式错误**
   - 检查CSV文件是否包含[Data]标识
   - 确认列名格式正确
   - 验证数据行格式

3. **可视化显示问题**
   - 检查数据范围是否合理
   - 调整流线密度参数
   - 重置相机视角

4. **性能问题**
   - 减少流线密度
   - 关闭不必要的显示选项
   - 使用数据采样

### 调试模式

在脚本中添加调试信息：
```python
# 在quick_visualize.py中启用详细输出
print(f"数据范围: {data.describe()}")
print(f"VTK网格信息: {ugrid.GetNumberOfPoints()} 点")
```

## 开发和定制

### 添加新的可视化类型
1. 在CFXVTKVisualizer类中添加新方法
2. 更新GUI控制选项
3. 在update_visualization中集成

### 支持新的数据格式
1. 修改parse_cfx_csv方法
2. 添加格式检测逻辑
3. 更新列名映射

### 性能优化建议
- 使用数据采样减少点数
- 实现LOD(Level of Detail)
- 添加进度条显示

## 许可证

本工具基于开源许可证，可自由使用和修改。

## 联系和支持

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件到开发团队
- 查看在线文档和教程
