#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
边界计算工具
统一处理各种数据源的边界计算逻辑
"""

import vtk
import numpy as np
from typing import List, Optional, Tuple, Union, Dict, Any
from utils.error_handler import handle_errors, ErrorContext


class BoundsCalculator:
    """边界计算器 - 统一处理各种数据源的边界计算"""
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="边界计算")
    def calculate_combined_bounds(data_sources: List[Any]) -> Optional[Tuple[float, float, float, float, float, float]]:
        """
        计算多个数据源的组合边界
        
        Args:
            data_sources: 数据源列表，可以是VTK对象、RegionData对象等
        
        Returns:
            组合边界 (xmin, xmax, ymin, ymax, zmin, zmax) 或 None
        """
        if not data_sources:
            return None
        
        bounds = [float('inf'), float('-inf')] * 3
        has_valid_bounds = False
        
        for data_source in data_sources:
            source_bounds = BoundsCalculator._extract_bounds(data_source)
            if source_bounds:
                bounds[0] = min(bounds[0], source_bounds[0])  # xmin
                bounds[1] = max(bounds[1], source_bounds[1])  # xmax
                bounds[2] = min(bounds[2], source_bounds[2])  # ymin
                bounds[3] = max(bounds[3], source_bounds[3])  # ymax
                bounds[4] = min(bounds[4], source_bounds[4])  # zmin
                bounds[5] = max(bounds[5], source_bounds[5])  # zmax
                has_valid_bounds = True
        
        return tuple(bounds) if has_valid_bounds and bounds[0] != float('inf') else None
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="VTK网格边界计算")
    def calculate_vtk_grids_bounds(vtk_grids: Dict[str, vtk.vtkUnstructuredGrid]) -> Optional[Tuple[float, float, float, float, float, float]]:
        """
        计算VTK网格字典的组合边界
        
        Args:
            vtk_grids: VTK网格字典 {region_name: vtk_grid}
        
        Returns:
            组合边界或None
        """
        if not vtk_grids:
            return None
        
        return BoundsCalculator.calculate_combined_bounds(list(vtk_grids.values()))
    
    @staticmethod
    @handle_errors(default_return=None, log_prefix="网格区域边界计算")
    def calculate_mesh_regions_bounds(mesh_regions: List[Any], data_manager: Any) -> Optional[Tuple[float, float, float, float, float, float]]:
        """
        计算网格区域的组合边界
        
        Args:
            mesh_regions: 网格区域名称列表
            data_manager: 数据管理器实例
        
        Returns:
            组合边界或None
        """
        if not mesh_regions or not data_manager:
            return None
        
        bounds = [float('inf'), float('-inf')] * 3
        has_valid_bounds = False
        
        for region_name in mesh_regions:
            # 使用数据管理器的统一方法获取网格数据
            mesh_region = data_manager.get_any_mesh_region(region_name)
            if mesh_region:
                region_bounds = BoundsCalculator._extract_bounds(mesh_region)
                if region_bounds:
                    bounds[0] = min(bounds[0], region_bounds[0])
                    bounds[1] = max(bounds[1], region_bounds[1])
                    bounds[2] = min(bounds[2], region_bounds[2])
                    bounds[3] = max(bounds[3], region_bounds[3])
                    bounds[4] = min(bounds[4], region_bounds[4])
                    bounds[5] = max(bounds[5], region_bounds[5])
                    has_valid_bounds = True
        
        return tuple(bounds) if has_valid_bounds and bounds[0] != float('inf') else None
    
    @staticmethod
    def _extract_bounds(data_source: Any) -> Optional[Tuple[float, float, float, float, float, float]]:
        """
        从数据源提取边界
        
        Args:
            data_source: 数据源对象
        
        Returns:
            边界元组或None
        """
        try:
            # VTK对象
            if hasattr(data_source, 'GetBounds'):
                bounds = data_source.GetBounds()
                if bounds and len(bounds) == 6:
                    return bounds
            
            # RegionData对象
            elif hasattr(data_source, 'get_bounds'):
                bounds = data_source.get_bounds()
                if bounds and len(bounds) == 6:
                    return bounds
            
            # 点数组
            elif hasattr(data_source, 'points') and data_source.points is not None:
                points = data_source.points
                if len(points) > 0:
                    if isinstance(points, np.ndarray):
                        min_coords = np.min(points, axis=0)
                        max_coords = np.max(points, axis=0)
                        if len(min_coords) >= 3 and len(max_coords) >= 3:
                            return (min_coords[0], max_coords[0],
                                   min_coords[1], max_coords[1],
                                   min_coords[2], max_coords[2])
            
            # 字典形式的点数据
            elif isinstance(data_source, dict) and 'points' in data_source:
                points = data_source['points']
                if isinstance(points, np.ndarray) and len(points) > 0:
                    min_coords = np.min(points, axis=0)
                    max_coords = np.max(points, axis=0)
                    if len(min_coords) >= 3 and len(max_coords) >= 3:
                        return (min_coords[0], max_coords[0],
                               min_coords[1], max_coords[1],
                               min_coords[2], max_coords[2])
            
            return None
            
        except Exception as e:
            print(f"提取边界失败: {e}")
            return None
    
    @staticmethod
    @handle_errors(default_return=False, log_prefix="边界验证")
    def validate_bounds(bounds: Optional[Tuple[float, float, float, float, float, float]]) -> bool:
        """
        验证边界是否有效
        
        Args:
            bounds: 边界元组
        
        Returns:
            是否有效
        """
        if not bounds or len(bounds) != 6:
            return False
        
        # 检查是否有无穷大值
        if any(abs(b) == float('inf') for b in bounds):
            return False
        
        # 检查最小值是否小于等于最大值
        if bounds[0] > bounds[1] or bounds[2] > bounds[3] or bounds[4] > bounds[5]:
            return False
        
        # 检查是否有NaN值
        if any(np.isnan(b) for b in bounds):
            return False
        
        return True
    
    @staticmethod
    def expand_bounds(bounds: Tuple[float, float, float, float, float, float], 
                     factor: float = 0.1) -> Tuple[float, float, float, float, float, float]:
        """
        扩展边界
        
        Args:
            bounds: 原始边界
            factor: 扩展因子
        
        Returns:
            扩展后的边界
        """
        if not BoundsCalculator.validate_bounds(bounds):
            return bounds
        
        try:
            x_range = bounds[1] - bounds[0]
            y_range = bounds[3] - bounds[2]
            z_range = bounds[5] - bounds[4]
            
            x_expand = x_range * factor
            y_expand = y_range * factor
            z_expand = z_range * factor
            
            return (
                bounds[0] - x_expand, bounds[1] + x_expand,
                bounds[2] - y_expand, bounds[3] + y_expand,
                bounds[4] - z_expand, bounds[5] + z_expand
            )
        except Exception as e:
            print(f"扩展边界失败: {e}")
            return bounds
    
    @staticmethod
    def get_bounds_center(bounds: Tuple[float, float, float, float, float, float]) -> Optional[Tuple[float, float, float]]:
        """
        获取边界中心点
        
        Args:
            bounds: 边界
        
        Returns:
            中心点坐标或None
        """
        if not BoundsCalculator.validate_bounds(bounds):
            return None
        
        try:
            center_x = (bounds[0] + bounds[1]) / 2
            center_y = (bounds[2] + bounds[3]) / 2
            center_z = (bounds[4] + bounds[5]) / 2
            return (center_x, center_y, center_z)
        except Exception as e:
            print(f"计算边界中心失败: {e}")
            return None
    
    @staticmethod
    def get_bounds_size(bounds: Tuple[float, float, float, float, float, float]) -> Optional[Tuple[float, float, float]]:
        """
        获取边界尺寸
        
        Args:
            bounds: 边界
        
        Returns:
            尺寸 (width, height, depth) 或None
        """
        if not BoundsCalculator.validate_bounds(bounds):
            return None
        
        try:
            width = bounds[1] - bounds[0]
            height = bounds[3] - bounds[2]
            depth = bounds[5] - bounds[4]
            return (width, height, depth)
        except Exception as e:
            print(f"计算边界尺寸失败: {e}")
            return None
