#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理装饰器和工具
统一异常处理模式，减少重复代码
"""

import functools
import traceback
from typing import Any, Callable, Optional, Union


def handle_errors(
    default_return: Any = None,
    log_prefix: str = "",
    print_traceback: bool = False,
    reraise: bool = False
):
    """
    错误处理装饰器
    
    Args:
        default_return: 发生错误时的默认返回值
        log_prefix: 日志前缀
        print_traceback: 是否打印详细错误堆栈
        reraise: 是否重新抛出异常
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                # 构建错误消息
                func_name = func.__name__
                error_msg = f"{log_prefix}{func_name}失败: {e}" if log_prefix else f"{func_name}失败: {e}"
                print(error_msg)
                
                # 打印详细堆栈（如果需要）
                if print_traceback:
                    traceback.print_exc()
                
                # 重新抛出异常（如果需要）
                if reraise:
                    raise
                
                return default_return
        return wrapper
    return decorator


def safe_execute(
    func: Callable,
    *args,
    default_return: Any = None,
    log_prefix: str = "",
    print_traceback: bool = False,
    **kwargs
) -> Any:
    """
    安全执行函数，捕获异常并返回默认值
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 发生错误时的默认返回值
        log_prefix: 日志前缀
        print_traceback: 是否打印详细错误堆栈
        **kwargs: 函数关键字参数
    
    Returns:
        函数执行结果或默认返回值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        func_name = getattr(func, '__name__', str(func))
        error_msg = f"{log_prefix}{func_name}失败: {e}" if log_prefix else f"{func_name}失败: {e}"
        print(error_msg)
        
        if print_traceback:
            traceback.print_exc()
        
        return default_return


class ErrorContext:
    """错误上下文管理器"""
    
    def __init__(
        self,
        operation_name: str,
        default_return: Any = None,
        print_traceback: bool = False,
        reraise: bool = False
    ):
        self.operation_name = operation_name
        self.default_return = default_return
        self.print_traceback = print_traceback
        self.reraise = reraise
        self.success = False
        self.error = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.error = exc_val
            error_msg = f"{self.operation_name}失败: {exc_val}"
            print(error_msg)
            
            if self.print_traceback:
                traceback.print_exc()
            
            if self.reraise:
                return False  # 重新抛出异常
            
            return True  # 抑制异常
        else:
            self.success = True
            return True


def log_operation(operation_name: str, success_msg: str = None, error_msg: str = None):
    """
    操作日志装饰器
    
    Args:
        operation_name: 操作名称
        success_msg: 成功消息模板
        error_msg: 错误消息模板
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                
                # 记录成功日志
                if success_msg:
                    print(f"✓ {success_msg}")
                elif result is not None and result is not False:
                    print(f"✓ {operation_name}成功")
                
                return result
            except Exception as e:
                # 记录错误日志
                if error_msg:
                    print(f"❌ {error_msg}: {e}")
                else:
                    print(f"❌ {operation_name}失败: {e}")
                raise
        return wrapper
    return decorator


# 预定义的常用装饰器
visualization_error_handler = handle_errors(
    default_return=False,
    log_prefix="可视化操作",
    print_traceback=False
)

data_processing_error_handler = handle_errors(
    default_return=None,
    log_prefix="数据处理",
    print_traceback=True
)

actor_creation_error_handler = handle_errors(
    default_return=None,
    log_prefix="Actor创建",
    print_traceback=False
)
