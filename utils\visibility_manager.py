#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可见性管理器
统一管理所有可视化对象的可见性更新逻辑
"""

import vtk
from typing import Dict, List, Optional, Any, Callable
from utils.error_handler import handle_errors, ErrorContext


class VisibilityState:
    """可见性状态数据类"""
    
    def __init__(self, 
                 show_pressure: bool = True,
                 show_contours: bool = False,
                 show_streamlines: bool = True,
                 show_vectors: bool = False,
                 show_mesh: bool = False,
                 extended_fields: Optional[Dict[str, bool]] = None):
        self.show_pressure = show_pressure
        self.show_contours = show_contours
        self.show_streamlines = show_streamlines
        self.show_vectors = show_vectors
        self.show_mesh = show_mesh
        self.extended_fields = extended_fields or {}
    
    def has_visible_pressure_fields(self) -> bool:
        """检查是否有可见的压力相关字段"""
        return self.show_pressure or self.show_contours
    
    def has_visible_velocity_fields(self) -> bool:
        """检查是否有可见的速度相关字段"""
        return self.show_streamlines or self.show_vectors
    
    def has_visible_extended_fields(self) -> bool:
        """检查是否有可见的扩展字段"""
        return any(self.extended_fields.values())
    
    def get_visible_extended_fields(self) -> List[str]:
        """获取可见的扩展字段列表"""
        return [field for field, visible in self.extended_fields.items() if visible]


class VisibilityManager:
    """可见性管理器 - 统一管理所有可视化对象的可见性"""
    
    def __init__(self):
        self.region_actors: Dict[str, Dict[str, vtk.vtkActor]] = {}
        self.extended_field_actors: Dict[str, Dict[str, vtk.vtkActor]] = {}
        self.mesh_actors: Dict[str, vtk.vtkActor] = {}
        
        # 可见性状态回调
        self.visibility_change_callbacks: List[Callable] = []
    
    def set_actor_collections(self, 
                             region_actors: Dict[str, Dict[str, vtk.vtkActor]],
                             extended_field_actors: Dict[str, Dict[str, vtk.vtkActor]],
                             mesh_actors: Dict[str, vtk.vtkActor]):
        """设置actor集合的引用"""
        self.region_actors = region_actors
        self.extended_field_actors = extended_field_actors
        self.mesh_actors = mesh_actors
    
    @handle_errors(default_return=False, log_prefix="可见性更新")
    def update_all_visibility(self, selected_regions: List[str], 
                             visibility_state: VisibilityState) -> bool:
        """
        统一更新所有可视化对象的可见性
        
        Args:
            selected_regions: 选中的区域列表
            visibility_state: 可见性状态
        
        Returns:
            是否更新成功
        """
        success_count = 0
        
        # 更新核心物理场可见性
        if self._update_core_fields_visibility(selected_regions, visibility_state):
            success_count += 1
        
        # 更新扩展物理场可见性
        if self._update_extended_fields_visibility(selected_regions, visibility_state):
            success_count += 1
        
        # 更新网格可见性
        if self._update_mesh_visibility(selected_regions, visibility_state):
            success_count += 1
        
        # 触发可见性变更回调
        self._trigger_visibility_callbacks(selected_regions, visibility_state)
        
        print(f"✓ 可见性更新完成: {success_count}/3 个类别成功更新")
        return success_count > 0
    
    @handle_errors(default_return=False, log_prefix="核心字段可见性更新")
    def _update_core_fields_visibility(self, selected_regions: List[str], 
                                      visibility_state: VisibilityState) -> bool:
        """更新核心物理场可见性"""
        if not self.region_actors:
            return False
        
        updated_count = 0
        
        for region_name, actors_dict in self.region_actors.items():
            is_selected = region_name in selected_regions
            
            # 压力分布
            if 'pressure' in actors_dict:
                visibility = is_selected and visibility_state.show_pressure
                actors_dict['pressure'].SetVisibility(visibility)
                if visibility:
                    print(f"✓ 区域 {region_name} 压力分布已显示")
                updated_count += 1
            
            # 压力轮廓
            if 'contour' in actors_dict:
                visibility = is_selected and visibility_state.show_contours
                actors_dict['contour'].SetVisibility(visibility)
                if visibility:
                    print(f"✓ 区域 {region_name} 压力轮廓已显示")
                updated_count += 1
            
            # 流线
            if 'streamline' in actors_dict:
                visibility = is_selected and visibility_state.show_streamlines
                actors_dict['streamline'].SetVisibility(visibility)
                if visibility:
                    print(f"✓ 区域 {region_name} 流线已显示")
                updated_count += 1
            
            # 速度矢量
            if 'vector' in actors_dict:
                visibility = is_selected and visibility_state.show_vectors
                actors_dict['vector'].SetVisibility(visibility)
                if visibility:
                    print(f"✓ 区域 {region_name} 速度矢量已显示")
                updated_count += 1
        
        print(f"核心字段可见性更新: {updated_count} 个actor已更新")
        return True
    
    @handle_errors(default_return=False, log_prefix="扩展字段可见性更新")
    def _update_extended_fields_visibility(self, selected_regions: List[str], 
                                          visibility_state: VisibilityState) -> bool:
        """更新扩展物理场可见性"""
        if not self.extended_field_actors or not visibility_state.extended_fields:
            return True  # 没有扩展字段时返回成功
        
        updated_count = 0
        
        for region_name, field_actors in self.extended_field_actors.items():
            is_selected = region_name in selected_regions
            
            for field_name, actor in field_actors.items():
                should_show = (is_selected and 
                              visibility_state.extended_fields.get(field_name, False))
                actor.SetVisibility(should_show)
                
                if should_show:
                    print(f"✓ 区域 {region_name} {field_name}字段已显示")
                updated_count += 1
        
        print(f"扩展字段可见性更新: {updated_count} 个actor已更新")
        return True
    
    @handle_errors(default_return=False, log_prefix="网格可见性更新")
    def _update_mesh_visibility(self, selected_regions: List[str], 
                               visibility_state: VisibilityState) -> bool:
        """更新网格可见性"""
        if not self.mesh_actors:
            return True  # 没有网格时返回成功
        
        updated_count = 0
        visible_count = 0
        
        for region_name, mesh_actor in self.mesh_actors.items():
            is_visible = region_name in selected_regions and visibility_state.show_mesh
            mesh_actor.SetVisibility(is_visible)
            
            if is_visible:
                visible_count += 1
                print(f"✓ 区域 {region_name} 网格已显示")
            
            updated_count += 1
        
        print(f"网格可见性更新: {visible_count}/{updated_count} 个网格可见")
        return True
    
    @handle_errors(default_return=False, log_prefix="仅网格可见性更新")
    def update_mesh_only_visibility(self, selected_regions: List[str]) -> bool:
        """仅更新网格可见性（用于仅渲染网格模式）"""
        if not self.mesh_actors:
            return False
        
        visible_count = 0
        
        for region_name, mesh_actor in self.mesh_actors.items():
            is_visible = region_name in selected_regions
            mesh_actor.SetVisibility(is_visible)
            
            if is_visible:
                visible_count += 1
                print(f"✓ 区域 {region_name} 网格已显示")
        
        print(f"仅网格模式: {visible_count}/{len(self.mesh_actors)} 个网格可见")
        return True
    
    def get_visibility_summary(self, selected_regions: List[str]) -> Dict[str, Any]:
        """获取可见性摘要信息"""
        summary = {
            'selected_regions': selected_regions,
            'core_fields': {},
            'extended_fields': {},
            'mesh': {},
            'total_visible_actors': 0
        }
        
        # 统计核心字段
        for region_name in selected_regions:
            if region_name in self.region_actors:
                region_summary = {}
                for field_type, actor in self.region_actors[region_name].items():
                    is_visible = actor.GetVisibility()
                    region_summary[field_type] = is_visible
                    if is_visible:
                        summary['total_visible_actors'] += 1
                summary['core_fields'][region_name] = region_summary
        
        # 统计扩展字段
        for region_name in selected_regions:
            if region_name in self.extended_field_actors:
                region_summary = {}
                for field_name, actor in self.extended_field_actors[region_name].items():
                    is_visible = actor.GetVisibility()
                    region_summary[field_name] = is_visible
                    if is_visible:
                        summary['total_visible_actors'] += 1
                summary['extended_fields'][region_name] = region_summary
        
        # 统计网格
        for region_name in selected_regions:
            if region_name in self.mesh_actors:
                is_visible = self.mesh_actors[region_name].GetVisibility()
                summary['mesh'][region_name] = is_visible
                if is_visible:
                    summary['total_visible_actors'] += 1
        
        return summary
    
    def add_visibility_callback(self, callback: Callable):
        """添加可见性变更回调"""
        if callback not in self.visibility_change_callbacks:
            self.visibility_change_callbacks.append(callback)
    
    def remove_visibility_callback(self, callback: Callable):
        """移除可见性变更回调"""
        if callback in self.visibility_change_callbacks:
            self.visibility_change_callbacks.remove(callback)
    
    def _trigger_visibility_callbacks(self, selected_regions: List[str], 
                                     visibility_state: VisibilityState):
        """触发可见性变更回调"""
        for callback in self.visibility_change_callbacks:
            try:
                callback(selected_regions, visibility_state)
            except Exception as e:
                print(f"可见性回调执行失败: {e}")
    
    def has_visible_actors(self, actor_type: str = None) -> bool:
        """检查是否有可见的actors"""
        if actor_type == 'core':
            return self._has_visible_core_actors()
        elif actor_type == 'extended':
            return self._has_visible_extended_actors()
        elif actor_type == 'mesh':
            return self._has_visible_mesh_actors()
        else:
            return (self._has_visible_core_actors() or 
                   self._has_visible_extended_actors() or 
                   self._has_visible_mesh_actors())
    
    def _has_visible_core_actors(self) -> bool:
        """检查是否有可见的核心actors"""
        for actors_dict in self.region_actors.values():
            for actor in actors_dict.values():
                if actor and actor.GetVisibility():
                    return True
        return False
    
    def _has_visible_extended_actors(self) -> bool:
        """检查是否有可见的扩展actors"""
        for field_actors in self.extended_field_actors.values():
            for actor in field_actors.values():
                if actor and actor.GetVisibility():
                    return True
        return False
    
    def _has_visible_mesh_actors(self) -> bool:
        """检查是否有可见的网格actors"""
        for actor in self.mesh_actors.values():
            if actor and actor.GetVisibility():
                return True
        return False
