#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网格可视化器
负责创建网格面片和点云的可视化
"""

import vtk
import numpy as np
from typing import Optional, List, Union
from vtk_renderer import VTKRenderer
from data_models import RegionData


class MeshVisualizer(VTKRenderer):
    """网格可视化器"""
    
    def __init__(self):
        super().__init__()
        
    def create_mesh_actor(self, region_data: RegionData) -> Optional[vtk.vtkActor]:
        """
        为区域数据创建网格可视化actor
        
        Args:
            region_data: 区域数据
            
        Returns:
            vtk.vtkActor: 网格actor或None
        """
        try:
            if region_data.has_faces():
                # 有面片数据，创建面片网格
                return self._create_surface_mesh_actor(region_data)
            else:
                # 没有面片数据，创建点云网格
                return self._create_point_mesh_actor(region_data)
                
        except Exception as e:
            print(f"创建区域 {region_data.name} 网格失败: {e}")
            return None
            
    def create_combined_mesh_actor(self, region_data_list: List[RegionData]) -> Optional[Union[vtk.vtkActor, vtk.vtkAssembly]]:
        """
        为多个区域创建组合网格可视化
        
        Args:
            region_data_list: 区域数据列表
            
        Returns:
            vtk.vtkActor或vtk.vtkAssembly: 组合网格actor或None
        """
        try:
            actors = []
            
            for region_data in region_data_list:
                actor = self.create_mesh_actor(region_data)
                if actor:
                    actors.append(actor)
                    
            if not actors:
                return None
                
            # 如果只有一个actor，直接返回
            if len(actors) == 1:
                return actors[0]
                
            # 多个actor时，创建组合
            assembly = vtk.vtkAssembly()
            for actor in actors:
                assembly.AddPart(actor)
                
            return assembly
            
        except Exception as e:
            print(f"创建组合网格失败: {e}")
            return None
            
    def _create_surface_mesh_actor(self, region_data: RegionData) -> Optional[vtk.vtkActor]:
        """为有面片数据的区域创建表面网格actor"""
        try:
            faces = region_data.faces
            
            # 创建VTK点
            vtk_points = vtk.vtkPoints()
            x, y, z = region_data.get_coordinates()
            
            for i in range(len(x)):
                vtk_points.InsertNextPoint(x[i], y[i], z[i])
                
            # 创建面片
            poly_data = vtk.vtkPolyData()
            poly_data.SetPoints(vtk_points)
            
            # 添加面片单元
            polys = vtk.vtkCellArray()
            valid_faces = 0
            invalid_faces = 0
            triangulated_faces = 0
            
            print(f"区域 {region_data.name}: 开始处理 {len(faces)} 个面片...")
            
            for face_idx, face in enumerate(faces):
                try:
                    # 检查面片索引是否有效（应该已经是0-based索引）
                    face_indices = []
                    invalid_indices = []

                    for idx in face:
                        if isinstance(idx, (int, float)):
                            idx = int(idx)
                            if 0 <= idx < len(x):
                                face_indices.append(idx)
                            else:
                                invalid_indices.append(idx)

                    # 调试信息：索引超出范围
                    if invalid_indices and face_idx < 5:  # 只输出前5个面片的调试信息
                        print(f"区域 {region_data.name}: 面片 {face_idx} 有无效索引 {invalid_indices}, 点数范围: 0-{len(x)-1}")
                        print(f"  原始面片: {face}")
                        print(f"  有效索引: {face_indices}")

                    # 检查面片是否有足够的点
                    if len(face_indices) < 3:
                        invalid_faces += 1
                        continue
                        
                    # 去除重复的点索引
                    unique_indices = []
                    for idx in face_indices:
                        if idx not in unique_indices:
                            unique_indices.append(idx)
                            
                    face_indices = unique_indices
                    
                    if len(face_indices) < 3:
                        invalid_faces += 1
                        continue
                        
                    if len(face_indices) == 3:
                        # 三角形
                        polys.InsertNextCell(3, face_indices)
                        valid_faces += 1
                    elif len(face_indices) == 4:
                        # 四边形
                        try:
                            polys.InsertNextCell(4, face_indices)
                            valid_faces += 1
                        except:
                            # 分解为三角形
                            polys.InsertNextCell(3, [face_indices[0], face_indices[1], face_indices[2]])
                            polys.InsertNextCell(3, [face_indices[0], face_indices[2], face_indices[3]])
                            triangulated_faces += 2
                            valid_faces += 2
                    elif len(face_indices) > 4:
                        # 多边形，使用扇形三角化
                        center_idx = face_indices[0]
                        for i in range(1, len(face_indices) - 1):
                            try:
                                polys.InsertNextCell(3, [center_idx, face_indices[i], face_indices[i+1]])
                                triangulated_faces += 1
                                valid_faces += 1
                            except:
                                invalid_faces += 1
                                continue
                                
                    # 显示进度
                    if (face_idx + 1) % 10000 == 0:
                        print(f"区域 {region_data.name}: 已处理 {face_idx + 1}/{len(faces)} 个面片...")
                        
                except Exception as e:
                    invalid_faces += 1
                    continue
                    
            print(f"区域 {region_data.name}: 面片处理完成")
            print(f"  有效面片: {valid_faces}")
            print(f"  无效面片: {invalid_faces}")
            if triangulated_faces > 0:
                print(f"  三角化面片: {triangulated_faces}")
                
            if valid_faces == 0:
                print(f"区域 {region_data.name}: 没有有效的面片")
                return None
                
            poly_data.SetPolys(polys)
            
            # 计算法向量
            normals = vtk.vtkPolyDataNormals()
            normals.SetInputData(poly_data)
            normals.ComputePointNormalsOn()
            normals.ComputeCellNormalsOn()
            normals.Update()
            
            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(normals.GetOutputPort())
            
            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            
            # 设置网格外观
            actor.GetProperty().SetColor(0.8, 0.8, 0.9)  # 浅蓝灰色
            actor.GetProperty().SetOpacity(0.6)  # 半透明
            actor.GetProperty().SetRepresentationToWireframe()  # 线框模式
            actor.GetProperty().SetLineWidth(1)
            
            print(f"区域 {region_data.name}: 创建了 {valid_faces} 个面片")
            return actor
            
        except Exception as e:
            print(f"创建区域 {region_data.name} 表面网格失败: {e}")
            return None
            
    def _create_point_mesh_actor(self, region_data: RegionData) -> Optional[vtk.vtkActor]:
        """为没有面片数据的区域创建点云网格actor"""
        try:
            # 创建VTK点
            vtk_points = vtk.vtkPoints()
            x, y, z = region_data.get_coordinates()
            
            for i in range(len(x)):
                vtk_points.InsertNextPoint(x[i], y[i], z[i])
                
            # 创建点云数据
            poly_data = vtk.vtkPolyData()
            poly_data.SetPoints(vtk_points)
            
            # 创建顶点单元
            vertices = vtk.vtkCellArray()
            for i in range(len(x)):
                vertices.InsertNextCell(1, [i])
            poly_data.SetVerts(vertices)
            
            # 计算合适的点大小
            bounds = poly_data.GetBounds()
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
            point_radius = domain_size / 1000  # 自适应点大小

            # 创建球体源用于点的可视化
            sphere = vtk.vtkSphereSource()
            sphere.SetRadius(point_radius)
            sphere.SetPhiResolution(8)
            sphere.SetThetaResolution(8)

            # 创建字形
            glyph = vtk.vtkGlyph3D()
            glyph.SetInputData(poly_data)
            glyph.SetSourceConnection(sphere.GetOutputPort())
            glyph.SetScaleModeToDataScalingOff()

            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(glyph.GetOutputPort())
            
            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            
            # 设置点云外观
            actor.GetProperty().SetColor(0.7, 0.7, 0.8)  # 浅灰色
            actor.GetProperty().SetOpacity(0.8)
            
            print(f"区域 {region_data.name}: 创建了 {len(x)} 个点的点云网格")
            return actor
            
        except Exception as e:
            print(f"创建区域 {region_data.name} 点云网格失败: {e}")
            return None
            
    def set_mesh_appearance(self, actor: vtk.vtkActor, wireframe: bool = True, 
                           opacity: float = 0.6, color: tuple = (0.8, 0.8, 0.9)):
        """
        设置网格外观
        
        Args:
            actor: VTK actor
            wireframe: 是否使用线框模式
            opacity: 透明度
            color: 颜色 (r, g, b)
        """
        if actor:
            actor.GetProperty().SetColor(*color)
            actor.GetProperty().SetOpacity(opacity)
            if wireframe:
                actor.GetProperty().SetRepresentationToWireframe()
            else:
                actor.GetProperty().SetRepresentationToSurface()
                
    def set_mesh_visibility(self, actor: vtk.vtkActor, visible: bool):
        """设置网格可见性"""
        if actor:
            actor.SetVisibility(visible)
