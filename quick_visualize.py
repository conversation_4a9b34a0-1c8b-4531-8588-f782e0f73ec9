#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFX快速可视化脚本
直接加载demo数据并生成可视化
"""

import os
import sys
import numpy as np
import pandas as pd
import vtk
from vtk.util import numpy_support


def parse_cfx_csv(filename):
    """解析CFX CSV文件格式"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except UnicodeDecodeError:
        # 尝试其他编码
        with open(filename, 'r', encoding='latin-1') as f:
            lines = f.readlines()

    # 查找数据开始行
    data_start = 0
    header_line = None

    for i, line in enumerate(lines):
        line_stripped = line.strip()
        if line_stripped.startswith('[Data]'):
            header_line = i + 1
            data_start = i + 2
            break

    if header_line is None:
        raise ValueError("未找到数据头部标识 [Data]")

    # 解析列名
    header = lines[header_line].strip()
    columns = [col.strip() for col in header.split(',')]
    expected_cols = len(columns)

    # 读取数据，只处理符合列数的行
    data_lines = lines[data_start:]
    data = []
    valid_count = 0
    skip_count = 0
    max_rows = None  # 不限制最大行数

    for line_num, line in enumerate(data_lines, start=data_start+1):
        line = line.strip()
        if line and not line.startswith('['):
            # 检查是否遇到新的数据段
            if line.startswith('['):
                print(f"遇到新数据段，停止解析: {line}")
                break

            try:
                # 分割并检查列数
                values_str = line.split(',')

                # 只处理列数匹配的行
                if len(values_str) == expected_cols:
                    values = []
                    valid_row = True

                    for val_str in values_str:
                        val_str = val_str.strip()
                        if val_str:
                            try:
                                values.append(float(val_str))
                            except ValueError:
                                # 如果包含非数字字符，跳过这行
                                valid_row = False
                                break
                        else:
                            values.append(0.0)  # 空值用0填充

                    if valid_row:
                        data.append(values)
                        valid_count += 1

                        # 显示进度（每10万行）
                        if valid_count % 100000 == 0:
                            print(f"已读取 {valid_count} 行数据...")
                    else:
                        skip_count += 1
                else:
                    skip_count += 1

            except Exception as e:
                skip_count += 1
                continue

    if not data:
        raise ValueError("未找到有效的数据行")

    print(f"解析完成: 有效数据 {valid_count} 行，跳过 {skip_count} 行")

    # 创建DataFrame
    df = pd.DataFrame(data, columns=columns)
    print(f"成功创建DataFrame: {df.shape}")
    return df


def find_coordinate_columns(data):
    """查找坐标列"""
    columns = data.columns.tolist()
    x_col = y_col = z_col = None

    # 查找X坐标列
    for col in columns:
        if 'X' in col and ('m' in col or 'M' in col):
            x_col = col
            break

    # 查找Y坐标列
    for col in columns:
        if 'Y' in col and ('m' in col or 'M' in col):
            y_col = col
            break

    # 查找Z坐标列
    for col in columns:
        if 'Z' in col and ('m' in col or 'M' in col):
            z_col = col
            break

    if not all([x_col, y_col, z_col]):
        raise ValueError(f"未找到完整的坐标列。找到: X={x_col}, Y={y_col}, Z={z_col}")

    return x_col, y_col, z_col


def create_vtk_grid(data):
    """创建VTK非结构化网格"""
    # 提取坐标
    x_col, y_col, z_col = find_coordinate_columns(data)
    print(f"使用坐标列: {x_col}, {y_col}, {z_col}")

    points = vtk.vtkPoints()
    for i in range(len(data)):
        points.InsertNextPoint(data[x_col].iloc[i],
                             data[y_col].iloc[i],
                             data[z_col].iloc[i])

    # 创建非结构化网格
    ugrid = vtk.vtkUnstructuredGrid()
    ugrid.SetPoints(points)

    # 添加点单元
    for i in range(len(data)):
        cell = vtk.vtkVertex()
        cell.GetPointIds().SetId(0, i)
        ugrid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())

    return ugrid


def add_scalar_data(ugrid, data, field_name):
    """向VTK网格添加标量数据"""
    if field_name not in data.columns:
        return False
    
    scalar_array = numpy_support.numpy_to_vtk(data[field_name].values)
    scalar_array.SetName(field_name)
    ugrid.GetPointData().AddArray(scalar_array)
    ugrid.GetPointData().SetActiveScalars(field_name)
    return True


def add_vector_data(ugrid, data, u_col, v_col, w_col, vector_name):
    """向VTK网格添加矢量数据"""
    if not all(col in data.columns for col in [u_col, v_col, w_col]):
        return False
    
    vectors = np.column_stack([data[u_col].values, 
                              data[v_col].values, 
                              data[w_col].values])
    
    vector_array = numpy_support.numpy_to_vtk(vectors)
    vector_array.SetName(vector_name)
    ugrid.GetPointData().AddArray(vector_array)
    ugrid.GetPointData().SetActiveVectors(vector_name)
    return True


def create_pressure_visualization(ugrid):
    """创建压力分布可视化"""
    # 创建点云可视化
    mapper = vtk.vtkDataSetMapper()
    mapper.SetInputData(ugrid)
    mapper.SetScalarModeToUsePointData()
    
    # 设置颜色范围
    scalar_range = ugrid.GetPointData().GetScalars().GetRange()
    mapper.SetScalarRange(scalar_range)
    
    # 创建演员
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    actor.GetProperty().SetPointSize(3)
    
    return actor


def create_streamline_visualization(ugrid):
    """创建流线可视化"""
    # 创建种子点
    seed_points = vtk.vtkPointSource()
    bounds = ugrid.GetBounds()
    seed_points.SetCenter((bounds[0] + bounds[1])/2, 
                         (bounds[2] + bounds[3])/2, 
                         (bounds[4] + bounds[5])/2)
    seed_points.SetRadius((bounds[1] - bounds[0])/4)
    seed_points.SetNumberOfPoints(50)
    
    # 创建流线
    streamline = vtk.vtkStreamTracer()
    streamline.SetInputData(ugrid)
    streamline.SetSourceConnection(seed_points.GetOutputPort())
    streamline.SetMaximumPropagation(1000)
    streamline.SetInitialIntegrationStep(0.01)
    streamline.SetIntegrationDirectionToBoth()
    
    # 创建流线管道
    tube = vtk.vtkTubeFilter()
    tube.SetInputConnection(streamline.GetOutputPort())
    tube.SetRadius(0.001)
    tube.SetNumberOfSides(6)
    
    # 创建映射器
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(tube.GetOutputPort())
    
    # 创建演员
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    actor.GetProperty().SetColor(1, 0, 0)  # 红色流线
    
    return actor


def quick_visualize(data_dir="demo_data/case1", use_steady=True):
    """快速可视化函数"""
    print("CFX流体可视化快速启动...")
    
    # 加载数据
    if use_steady:
        data_file = os.path.join(data_dir, "steady_results.csv")
        print(f"加载稳态数据: {data_file}")
    else:
        data_file = os.path.join(data_dir, "transient_results.csv")
        print(f"加载瞬态数据: {data_file}")
    
    if not os.path.exists(data_file):
        print(f"错误: 数据文件不存在 {data_file}")
        return
    
    # 解析数据
    data = parse_cfx_csv(data_file)
    print(f"数据点数量: {len(data)}")
    print(f"数据列: {list(data.columns)}")
    
    # 创建VTK网格
    ugrid = create_vtk_grid(data)
    
    # 添加压力数据
    pressure_col = None
    for col in data.columns:
        if 'Pressure' in col and 'Gradient' not in col and 'Total' not in col:
            pressure_col = col
            break
    
    if pressure_col:
        add_scalar_data(ugrid, data, pressure_col)
        print(f"添加压力数据: {pressure_col}")
    
    # 添加速度矢量数据
    velocity_cols = []
    for suffix in ['u', 'v', 'w']:
        for col in data.columns:
            if f'Velocity {suffix}' in col:
                velocity_cols.append(col)
                break
    
    if len(velocity_cols) == 3:
        add_vector_data(ugrid, data, velocity_cols[0], velocity_cols[1], velocity_cols[2], "Velocity")
        print(f"添加速度矢量: {velocity_cols}")
    
    # 创建渲染器
    renderer = vtk.vtkRenderer()
    renderer.SetBackground(0.1, 0.1, 0.2)
    
    # 添加压力分布
    if pressure_col:
        pressure_actor = create_pressure_visualization(ugrid)
        renderer.AddActor(pressure_actor)
        print("添加压力分布可视化")
        
        # 添加颜色条
        scalar_bar = vtk.vtkScalarBarActor()
        scalar_bar.SetLookupTable(pressure_actor.GetMapper().GetLookupTable())
        scalar_bar.SetTitle("压力 [Pa]")
        scalar_bar.SetNumberOfLabels(5)
        renderer.AddActor2D(scalar_bar)
    
    # 添加流线
    if len(velocity_cols) == 3:
        streamline_actor = create_streamline_visualization(ugrid)
        renderer.AddActor(streamline_actor)
        print("添加流线可视化")
    
    # 添加坐标轴
    axes = vtk.vtkAxesActor()
    axes.SetTotalLength(0.05, 0.05, 0.05)
    renderer.AddActor(axes)
    
    # 创建渲染窗口
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(800, 600)
    render_window.SetWindowName("CFX 流体可视化")
    
    # 创建交互器
    interactor = vtk.vtkRenderWindowInteractor()
    interactor.SetRenderWindow(render_window)
    
    # 设置交互样式
    style = vtk.vtkInteractorStyleTrackballCamera()
    interactor.SetInteractorStyle(style)
    
    # 重置相机并开始交互
    renderer.ResetCamera()
    render_window.Render()
    
    print("可视化窗口已打开，按 'q' 退出")
    interactor.Start()


def main():
    """主函数"""
    # 检查VTK是否可用
    try:
        import vtk
        print(f"VTK版本: {vtk.vtkVersion.GetVTKVersion()}")
    except ImportError:
        print("错误: 未安装VTK库")
        print("请运行: pip install vtk")
        sys.exit(1)
    
    # 检查数据目录
    if not os.path.exists("demo_data/case1"):
        print("错误: 未找到demo_data/case1目录")
        print("请确保在正确的工作目录中运行此脚本")
        sys.exit(1)
    
    # 快速可视化
    quick_visualize()


if __name__ == "__main__":
    main()
