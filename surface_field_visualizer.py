#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
面单元物理场可视化器
负责在面单元上渲染各种物理场数据
"""

import vtk
import numpy as np
from typing import Optional, Tuple, Dict, Any
from vtk_renderer import VTK<PERSON>enderer
from data_models import RegionData


class SurfaceFieldVisualizer(VTKRenderer):
    """面单元物理场可视化器"""
    
    def __init__(self):
        super().__init__()
        
    def create_surface_field_actor(self, region_data: RegionData, field_name: str,
                                 global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """
        在面单元上创建物理场可视化
        
        Args:
            region_data: 区域数据
            field_name: 物理场名称 ('pressure', 'temperature', 'density', etc.)
            global_scalar_range: 全局标量范围
            
        Returns:
            vtk.vtkActor: 面单元物理场actor或None
        """
        try:
            # 获取物理场数据
            field_data = region_data.get_physical_field_data(field_name)
            if field_data is None:
                print(f"警告: 区域 {region_data.name} 没有 {field_name} 数据")
                return None
                
            # 检查是否有面片数据
            if not region_data.has_faces():
                print(f"警告: 区域 {region_data.name} 没有面片数据，无法创建面单元可视化")
                return self._create_point_field_visualization(region_data, field_name, global_scalar_range)
                
            # 创建面单元可视化
            return self._create_surface_mesh_with_field(region_data, field_name, field_data, global_scalar_range)
            
        except Exception as e:
            print(f"创建面单元物理场可视化失败: {e}")
            return None
            
    def _create_surface_mesh_with_field(self, region_data: RegionData, field_name: str,
                                      field_data: np.ndarray, 
                                      global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """创建带物理场数据的面网格"""
        try:
            # 获取坐标和面片数据
            x, y, z = region_data.get_coordinates()
            faces = region_data.faces
            
            # 创建VTK点
            points = vtk.vtkPoints()
            for i in range(len(x)):
                points.InsertNextPoint(x[i], y[i], z[i])
                
            # 创建多边形数据
            poly_data = vtk.vtkPolyData()
            poly_data.SetPoints(points)
            
            # 添加面片
            polys = vtk.vtkCellArray()
            for face in faces:
                if len(face) >= 3:  # 确保是有效的多边形
                    poly = vtk.vtkPolygon()
                    poly.GetPointIds().SetNumberOfIds(len(face))
                    for i, vertex_id in enumerate(face):
                        poly.GetPointIds().SetId(i, vertex_id)
                    polys.InsertNextCell(poly)
                    
            poly_data.SetPolys(polys)
            
            # 添加物理场数据作为点数据
            from vtk.util import numpy_support
            scalar_array = numpy_support.numpy_to_vtk(field_data)

            # 根据物理场类型设置合适的名称
            if field_name == 'pressure':
                scalar_array.SetName("Pressure")
            else:
                scalar_array.SetName(field_name.title())

            poly_data.GetPointData().AddArray(scalar_array)
            poly_data.GetPointData().SetActiveScalars(scalar_array.GetName())
            
            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(poly_data)
            mapper.SetScalarModeToUsePointData()
            
            # 设置颜色范围
            if global_scalar_range:
                scalar_range = global_scalar_range
                print(f"使用全局 {field_name} 范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f}")
            else:
                scalar_range = (field_data.min(), field_data.max())
                print(f"使用局部 {field_name} 范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f}")
                
            mapper.SetScalarRange(scalar_range)
            
            # 设置颜色映射
            lut = self._get_colormap_for_field(field_name)
            lut.SetRange(scalar_range)
            mapper.SetLookupTable(lut)
            
            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            
            # 设置面单元外观
            actor.GetProperty().SetRepresentationToSurface()
            actor.GetProperty().SetOpacity(0.8)
            
            print(f"区域 {region_data.name}: 创建了 {len(faces)} 个面的 {field_name} 可视化")
            return actor
            
        except Exception as e:
            print(f"创建面网格物理场可视化失败: {e}")
            return None
            
    def _create_point_field_visualization(self, region_data: RegionData, field_name: str,
                                        global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """创建点云物理场可视化（回退方案）"""
        try:
            # 获取数据
            x, y, z = region_data.get_coordinates()
            field_data = region_data.get_physical_field_data(field_name)
            
            if field_data is None:
                return None
                
            # 创建VTK点
            points = vtk.vtkPoints()
            for i in range(len(x)):
                points.InsertNextPoint(x[i], y[i], z[i])
                
            # 创建多边形数据
            poly_data = vtk.vtkPolyData()
            poly_data.SetPoints(points)
            
            # 添加顶点单元
            verts = vtk.vtkCellArray()
            for i in range(len(x)):
                verts.InsertNextCell(1, [i])
            poly_data.SetVerts(verts)
            
            # 添加物理场数据
            from vtk.util import numpy_support
            scalar_array = numpy_support.numpy_to_vtk(field_data)
            scalar_array.SetName(field_name.title())
            poly_data.GetPointData().AddArray(scalar_array)
            poly_data.GetPointData().SetActiveScalars(field_name.title())
            
            # 计算合适的点大小
            bounds = poly_data.GetBounds()
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
            point_radius = domain_size / 500
            
            # 创建球体源
            sphere = vtk.vtkSphereSource()
            sphere.SetRadius(point_radius)
            sphere.SetPhiResolution(8)
            sphere.SetThetaResolution(8)
            
            # 创建字形
            glyph = vtk.vtkGlyph3D()
            glyph.SetInputData(poly_data)
            glyph.SetSourceConnection(sphere.GetOutputPort())
            glyph.SetScaleModeToDataScalingOff()
            glyph.SetColorModeToColorByScalar()
            
            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(glyph.GetOutputPort())
            mapper.SetScalarModeToUsePointData()
            
            # 设置颜色范围
            if global_scalar_range:
                scalar_range = global_scalar_range
            else:
                scalar_range = (field_data.min(), field_data.max())
                
            mapper.SetScalarRange(scalar_range)
            
            # 设置颜色映射
            lut = self._get_colormap_for_field(field_name)
            lut.SetRange(scalar_range)
            mapper.SetLookupTable(lut)
            
            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetOpacity(0.8)
            
            print(f"区域 {region_data.name}: 创建了 {len(x)} 个点的 {field_name} 点云可视化")
            return actor
            
        except Exception as e:
            print(f"创建点云物理场可视化失败: {e}")
            return None
            
    def _get_colormap_for_field(self, field_name: str) -> vtk.vtkLookupTable:
        """根据物理场类型获取合适的颜色映射"""
        lut = vtk.vtkLookupTable()
        lut.SetNumberOfTableValues(256)

        if field_name == 'pressure':
            # 压力：使用彩虹色映射（与原有压力可视化保持一致）
            lut.SetHueRange(0.67, 0.0)  # 蓝到红
            lut.SetSaturationRange(1.0, 1.0)
            lut.SetValueRange(1.0, 1.0)
        elif field_name == 'temperature':
            # 温度：深蓝(冷) -> 红色(热)
            lut.SetHueRange(0.67, 0.0)
            lut.SetSaturationRange(1.0, 1.0)
            lut.SetValueRange(1.0, 1.0)
        elif field_name == 'velocity_magnitude':
            # 速度：绿色(低) -> 红色(高)
            lut.SetHueRange(0.33, 0.0)
            lut.SetSaturationRange(1.0, 1.0)
            lut.SetValueRange(1.0, 1.0)
        elif field_name == 'density':
            # 密度：紫色(低) -> 黄色(高)
            lut.SetHueRange(0.83, 0.17)
            lut.SetSaturationRange(1.0, 1.0)
            lut.SetValueRange(1.0, 1.0)
        else:
            # 默认：彩虹色
            lut.SetHueRange(0.67, 0.0)
            lut.SetSaturationRange(1.0, 1.0)
            lut.SetValueRange(1.0, 1.0)

        lut.Build()
        return lut
        
    def create_contour_field_actor(self, region_data: RegionData, field_name: str,
                                 contour_count: int = 10, line_width: float = 2.0,
                                 global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """创建物理场等值线可视化"""
        try:
            # 获取物理场数据
            field_data = region_data.get_physical_field_data(field_name)
            if field_data is None:
                print(f"警告: 区域 {region_data.name} 没有 {field_name} 数据")
                return None
                
            # 创建VTK网格
            ugrid = self.create_vtk_grid(region_data, 1)  # 不采样，保持精度
            if ugrid is None:
                return None
                
            # 设置标量范围
            if global_scalar_range:
                scalar_range = global_scalar_range
            else:
                scalar_range = (field_data.min(), field_data.max())
                
            # 创建等值线过滤器
            contour_filter = vtk.vtkContourFilter()
            contour_filter.SetInputData(ugrid)
            contour_filter.SetNumberOfContours(contour_count)
            contour_filter.GenerateValues(contour_count, scalar_range[0], scalar_range[1])
            contour_filter.Update()
            
            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(contour_filter.GetOutputPort())
            mapper.SetScalarModeToUsePointData()
            mapper.SetScalarRange(scalar_range)
            
            # 设置颜色映射
            lut = self._get_colormap_for_field(field_name)
            lut.SetRange(scalar_range)
            mapper.SetLookupTable(lut)
            
            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetLineWidth(line_width)
            actor.GetProperty().SetRepresentationToWireframe()
            
            print(f"区域 {region_data.name}: 创建了 {field_name} 等值线可视化")
            return actor
            
        except Exception as e:
            print(f"创建物理场等值线可视化失败: {e}")
            return None

    def create_pressure_surface_actor(self, region_data: RegionData,
                                    global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """
        专门为压力场创建面单元可视化（与原有压力可视化保持一致）

        Args:
            region_data: 区域数据
            global_scalar_range: 全局压力范围

        Returns:
            vtk.vtkActor: 压力面单元actor或None
        """
        try:
            # 获取压力数据
            pressure_data = region_data.get_pressure_data()
            if pressure_data is None:
                print(f"警告: 区域 {region_data.name} 没有压力数据")
                return None

            # 检查是否有面片数据
            if not region_data.has_faces():
                print(f"警告: 区域 {region_data.name} 没有面片数据，使用点云渲染")
                return self._create_point_field_visualization(region_data, 'pressure', global_scalar_range)

            print(f"区域 {region_data.name} 有面片数据，使用面单元渲染压力")

            # 创建面单元压力可视化
            return self._create_pressure_surface_mesh(region_data, pressure_data, global_scalar_range)

        except Exception as e:
            print(f"创建压力面单元可视化失败: {e}")
            return None

    def _create_pressure_surface_mesh(self, region_data: RegionData, pressure_data: np.ndarray,
                                    global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """创建压力面网格可视化"""
        try:
            # 获取坐标和面片数据
            x, y, z = region_data.get_coordinates()
            faces = region_data.faces

            print(f"区域 {region_data.name}: 创建压力面单元可视化 - {len(x)} 个点, {len(faces)} 个面")

            # 创建VTK点
            points = vtk.vtkPoints()
            for i in range(len(x)):
                points.InsertNextPoint(x[i], y[i], z[i])

            # 创建多边形数据
            poly_data = vtk.vtkPolyData()
            poly_data.SetPoints(points)

            # 添加面片
            polys = vtk.vtkCellArray()
            valid_faces = 0
            for face in faces:
                if len(face) >= 3:  # 确保是有效的多边形
                    poly = vtk.vtkPolygon()
                    poly.GetPointIds().SetNumberOfIds(len(face))
                    for i, vertex_id in enumerate(face):
                        if vertex_id < len(x):  # 确保索引有效
                            poly.GetPointIds().SetId(i, vertex_id)
                        else:
                            print(f"警告: 无效的顶点索引 {vertex_id}")
                            break
                    else:
                        polys.InsertNextCell(poly)
                        valid_faces += 1

            poly_data.SetPolys(polys)
            print(f"有效面片数: {valid_faces}")

            # 添加压力数据
            from vtk.util import numpy_support
            scalar_array = numpy_support.numpy_to_vtk(pressure_data)
            scalar_array.SetName("Pressure")
            poly_data.GetPointData().AddArray(scalar_array)
            poly_data.GetPointData().SetActiveScalars("Pressure")

            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(poly_data)
            mapper.SetScalarModeToUsePointData()

            # 设置压力范围
            if global_scalar_range:
                scalar_range = global_scalar_range
                print(f"使用全局压力范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f} Pa")
            else:
                scalar_range = (pressure_data.min(), pressure_data.max())
                print(f"使用局部压力范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f} Pa")

            mapper.SetScalarRange(scalar_range)

            # 使用与原有压力可视化相同的彩虹色映射
            from vtk_renderer import VTKRenderer
            renderer = VTKRenderer()
            lut = renderer.create_rainbow_colormap()
            lut.SetRange(scalar_range)
            mapper.SetLookupTable(lut)

            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            # 设置面单元外观
            actor.GetProperty().SetRepresentationToSurface()
            actor.GetProperty().SetOpacity(0.9)  # 稍微透明以便观察

            print(f"区域 {region_data.name}: 压力面单元可视化创建完成")
            return actor

        except Exception as e:
            print(f"创建压力面网格可视化失败: {e}")
            return None
