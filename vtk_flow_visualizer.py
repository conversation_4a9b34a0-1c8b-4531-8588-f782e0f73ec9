#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFX后处理VTK可视化工具
实现流体流速迹线云图和压力分布云图的交互式可视化
"""

import numpy as np
import pandas as pd
import vtk
from vtk.util import numpy_support
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys


class CFXVTKVisualizer:
    """CFX仿真结果VTK可视化器"""
    
    def __init__(self):
        self.mesh_data = None
        self.steady_data = None
        self.transient_data = None
        self.vtk_grid = None
        self.renderer = None
        self.render_window = None
        self.interactor = None
        
        # 可视化对象
        self.pressure_actor = None
        self.streamline_actor = None
        self.velocity_vectors = None
        
        # 创建主窗口
        self.setup_gui()
        
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("CFX VTK 流体可视化工具")
        self.root.geometry("800x600")
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 数据加载区域
        data_frame = ttk.LabelFrame(main_frame, text="数据加载", padding=10)
        data_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(data_frame, text="加载网格数据", 
                  command=self.load_mesh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(data_frame, text="加载稳态结果", 
                  command=self.load_steady_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(data_frame, text="加载瞬态结果", 
                  command=self.load_transient_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # 可视化控制区域
        viz_frame = ttk.LabelFrame(main_frame, text="可视化控制", padding=10)
        viz_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 数据源选择
        ttk.Label(viz_frame, text="数据源:").grid(row=0, column=0, sticky=tk.W)
        self.data_source_var = tk.StringVar(value="steady")
        ttk.Radiobutton(viz_frame, text="稳态", variable=self.data_source_var, 
                       value="steady").grid(row=0, column=1, sticky=tk.W)
        ttk.Radiobutton(viz_frame, text="瞬态", variable=self.data_source_var, 
                       value="transient").grid(row=0, column=2, sticky=tk.W)
        
        # 可视化选项
        ttk.Label(viz_frame, text="显示选项:").grid(row=1, column=0, sticky=tk.W)
        self.show_pressure_var = tk.BooleanVar(value=True)
        self.show_streamlines_var = tk.BooleanVar(value=True)
        self.show_vectors_var = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(viz_frame, text="压力分布", 
                       variable=self.show_pressure_var).grid(row=1, column=1, sticky=tk.W)
        ttk.Checkbutton(viz_frame, text="流线", 
                       variable=self.show_streamlines_var).grid(row=1, column=2, sticky=tk.W)
        ttk.Checkbutton(viz_frame, text="速度矢量", 
                       variable=self.show_vectors_var).grid(row=1, column=3, sticky=tk.W)
        
        # 参数控制
        params_frame = ttk.Frame(viz_frame)
        params_frame.grid(row=2, column=0, columnspan=4, sticky=tk.EW, pady=(10, 0))
        
        ttk.Label(params_frame, text="流线密度:").pack(side=tk.LEFT)
        self.streamline_density = tk.Scale(params_frame, from_=10, to=100, 
                                          orient=tk.HORIZONTAL, length=100)
        self.streamline_density.set(50)
        self.streamline_density.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(params_frame, text="矢量缩放:").pack(side=tk.LEFT)
        self.vector_scale = tk.Scale(params_frame, from_=0.1, to=2.0,
                                   resolution=0.1, orient=tk.HORIZONTAL, length=100)
        self.vector_scale.set(1.0)
        self.vector_scale.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(params_frame, text="数据采样:").pack(side=tk.LEFT)
        self.data_sampling = tk.Scale(params_frame, from_=1, to=10,
                                    orient=tk.HORIZONTAL, length=100)
        self.data_sampling.set(1)
        self.data_sampling.pack(side=tk.LEFT, padx=(5, 10))
        
        # 操作按钮
        button_frame = ttk.Frame(viz_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(button_frame, text="生成可视化", 
                  command=self.create_visualization).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="更新显示", 
                  command=self.update_visualization).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存图像", 
                  command=self.save_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出VTK", 
                  command=self.export_vtk).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        # VTK渲染窗口将在需要时创建
        
    def load_mesh_data(self):
        """加载网格数据"""
        filename = filedialog.askopenfilename(
            title="选择网格文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialdir="demo_data/case1" if os.path.exists("demo_data/case1") else "."
        )
        if filename:
            try:
                self.mesh_data = self.parse_cfx_csv(filename)
                self.status_var.set(f"✓ 已加载网格数据: {len(self.mesh_data)} 个点")
                print(f"网格数据列: {list(self.mesh_data.columns)}")
            except Exception as e:
                error_msg = f"加载网格数据失败:\n{str(e)}\n\n请确保文件格式正确，包含[Data]标识和坐标数据"
                messagebox.showerror("加载错误", error_msg)
                print(f"网格数据加载错误: {e}")

    def load_steady_data(self):
        """加载稳态结果数据"""
        filename = filedialog.askopenfilename(
            title="选择稳态结果文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialdir="demo_data/case1" if os.path.exists("demo_data/case1") else "."
        )
        if filename:
            try:
                self.steady_data = self.parse_cfx_csv(filename)
                self.status_var.set(f"✓ 已加载稳态数据: {len(self.steady_data)} 个点")
                print(f"稳态数据列: {list(self.steady_data.columns)}")
            except Exception as e:
                error_msg = f"加载稳态数据失败:\n{str(e)}\n\n请确保文件格式正确，包含[Data]标识和物理量数据"
                messagebox.showerror("加载错误", error_msg)
                print(f"稳态数据加载错误: {e}")

    def load_transient_data(self):
        """加载瞬态结果数据"""
        filename = filedialog.askopenfilename(
            title="选择瞬态结果文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialdir="demo_data/case1" if os.path.exists("demo_data/case1") else "."
        )
        if filename:
            try:
                self.transient_data = self.parse_cfx_csv(filename)
                self.status_var.set(f"✓ 已加载瞬态数据: {len(self.transient_data)} 个点")
                print(f"瞬态数据列: {list(self.transient_data.columns)}")
            except Exception as e:
                error_msg = f"加载瞬态数据失败:\n{str(e)}\n\n请确保文件格式正确，包含[Data]标识和物理量数据"
                messagebox.showerror("加载错误", error_msg)
                print(f"瞬态数据加载错误: {e}")
    
    def parse_cfx_csv(self, filename):
        """解析CFX CSV文件格式"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except UnicodeDecodeError:
            # 尝试其他编码
            with open(filename, 'r', encoding='latin-1') as f:
                lines = f.readlines()

        # 查找数据开始行
        data_start = 0
        header_line = None

        for i, line in enumerate(lines):
            line_stripped = line.strip()
            if line_stripped.startswith('[Data]'):
                header_line = i + 1
                data_start = i + 2
                break

        if header_line is None:
            raise ValueError("未找到数据头部标识 [Data]")

        # 解析列名
        header = lines[header_line].strip()
        columns = [col.strip() for col in header.split(',')]
        expected_cols = len(columns)

        # 读取数据，只处理符合列数的行
        data_lines = lines[data_start:]
        data = []
        valid_count = 0
        skip_count = 0
        max_rows = None  # 不限制最大行数

        for line_num, line in enumerate(data_lines, start=data_start+1):
            line = line.strip()
            if line and not line.startswith('['):
                # 检查是否遇到新的数据段
                if line.startswith('['):
                    print(f"遇到新数据段，停止解析: {line}")
                    break

                try:
                    # 分割并检查列数
                    values_str = line.split(',')

                    # 只处理列数匹配的行
                    if len(values_str) == expected_cols:
                        values = []
                        valid_row = True

                        for val_str in values_str:
                            val_str = val_str.strip()
                            if val_str:
                                try:
                                    values.append(float(val_str))
                                except ValueError:
                                    # 如果包含非数字字符，跳过这行
                                    valid_row = False
                                    break
                            else:
                                values.append(0.0)  # 空值用0填充

                        if valid_row:
                            data.append(values)
                            valid_count += 1

                            # 显示进度（每10万行）
                            if valid_count % 100000 == 0:
                                print(f"已读取 {valid_count} 行数据...")
                        else:
                            skip_count += 1
                    else:
                        skip_count += 1

                except Exception as e:
                    skip_count += 1
                    continue

        if not data:
            raise ValueError("未找到有效的数据行")

        print(f"解析完成: 有效数据 {valid_count} 行，跳过 {skip_count} 行")

        # 创建DataFrame
        df = pd.DataFrame(data, columns=columns)
        print(f"成功创建DataFrame: {df.shape}")
        return df

    def find_coordinate_columns(self, data):
        """查找坐标列"""
        columns = data.columns.tolist()
        x_col = y_col = z_col = None

        # 查找X坐标列
        for col in columns:
            if 'X' in col and ('m' in col or 'M' in col):
                x_col = col
                break

        # 查找Y坐标列
        for col in columns:
            if 'Y' in col and ('m' in col or 'M' in col):
                y_col = col
                break

        # 查找Z坐标列
        for col in columns:
            if 'Z' in col and ('m' in col or 'M' in col):
                z_col = col
                break

        if not all([x_col, y_col, z_col]):
            raise ValueError(f"未找到完整的坐标列。找到: X={x_col}, Y={y_col}, Z={z_col}")

        return x_col, y_col, z_col

    def create_vtk_grid(self, data):
        """创建VTK非结构化网格"""
        # 提取坐标
        x_col, y_col, z_col = self.find_coordinate_columns(data)
        print(f"使用坐标列: {x_col}, {y_col}, {z_col}")

        # 数据采样
        sampling_rate = self.data_sampling.get() if hasattr(self, 'data_sampling') else 1
        if sampling_rate > 1:
            sampled_data = data.iloc[::sampling_rate].copy()
            print(f"数据采样: 从 {len(data)} 行采样到 {len(sampled_data)} 行 (采样率: 1/{sampling_rate})")
        else:
            sampled_data = data

        points = vtk.vtkPoints()
        for i in range(len(sampled_data)):
            points.InsertNextPoint(sampled_data[x_col].iloc[i],
                                 sampled_data[y_col].iloc[i],
                                 sampled_data[z_col].iloc[i])

        # 创建非结构化网格
        ugrid = vtk.vtkUnstructuredGrid()
        ugrid.SetPoints(points)

        # 添加点单元
        for i in range(len(sampled_data)):
            cell = vtk.vtkVertex()
            cell.GetPointIds().SetId(0, i)
            ugrid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())

        # 存储采样后的数据以便后续使用
        self.current_sampled_data = sampled_data

        return ugrid

    def add_scalar_data(self, ugrid, data, field_name):
        """向VTK网格添加标量数据"""
        if field_name not in data.columns:
            return False

        # 使用采样后的数据
        actual_data = getattr(self, 'current_sampled_data', data)
        if field_name not in actual_data.columns:
            return False

        scalar_array = numpy_support.numpy_to_vtk(actual_data[field_name].values)
        scalar_array.SetName(field_name)
        ugrid.GetPointData().AddArray(scalar_array)
        ugrid.GetPointData().SetActiveScalars(field_name)
        return True

    def add_vector_data(self, ugrid, data, u_col, v_col, w_col, vector_name):
        """向VTK网格添加矢量数据"""
        # 使用采样后的数据
        actual_data = getattr(self, 'current_sampled_data', data)
        if not all(col in actual_data.columns for col in [u_col, v_col, w_col]):
            return False

        vectors = np.column_stack([actual_data[u_col].values,
                                  actual_data[v_col].values,
                                  actual_data[w_col].values])

        vector_array = numpy_support.numpy_to_vtk(vectors)
        vector_array.SetName(vector_name)
        ugrid.GetPointData().AddArray(vector_array)
        ugrid.GetPointData().SetActiveVectors(vector_name)
        return True

    def create_pressure_visualization(self, ugrid):
        """创建压力分布可视化"""
        try:
            # 检查是否有标量数据
            if ugrid.GetPointData().GetScalars() is None:
                print("警告: 没有找到标量数据，使用点云可视化")
                return self.create_point_cloud_visualization(ugrid)

            # 使用简单的点云可视化，避免Delaunay三角化的复杂性
            mapper = vtk.vtkDataSetMapper()
            mapper.SetInputData(ugrid)
            mapper.SetScalarModeToUsePointData()

            # 设置颜色范围
            scalar_range = ugrid.GetPointData().GetScalars().GetRange()
            mapper.SetScalarRange(scalar_range)

            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetPointSize(2)  # 设置点大小

            return actor

        except Exception as e:
            print(f"创建压力可视化失败: {e}")
            return self.create_point_cloud_visualization(ugrid)

    def create_point_cloud_visualization(self, ugrid):
        """创建简单的点云可视化"""
        mapper = vtk.vtkDataSetMapper()
        mapper.SetInputData(ugrid)

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetPointSize(3)
        actor.GetProperty().SetColor(0.8, 0.8, 0.8)  # 灰色点

        return actor

    def create_streamline_visualization(self, ugrid):
        """创建流线可视化"""
        try:
            # 检查是否有矢量数据
            if ugrid.GetPointData().GetVectors() is None:
                print("警告: 没有找到矢量数据，无法创建流线")
                return None

            # 创建种子点
            seed_points = vtk.vtkPointSource()
            bounds = ugrid.GetBounds()

            # 计算合理的中心点和半径
            center_x = (bounds[0] + bounds[1]) / 2
            center_y = (bounds[2] + bounds[3]) / 2
            center_z = (bounds[4] + bounds[5]) / 2
            radius = min(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4]) / 8

            seed_points.SetCenter(center_x, center_y, center_z)
            seed_points.SetRadius(radius)
            seed_points.SetNumberOfPoints(min(self.streamline_density.get(), 20))  # 限制种子点数量

            # 创建流线
            streamline = vtk.vtkStreamTracer()
            streamline.SetInputData(ugrid)
            streamline.SetSourceConnection(seed_points.GetOutputPort())
            streamline.SetMaximumPropagation(100)  # 减少传播距离
            streamline.SetInitialIntegrationStep(radius / 100)  # 自适应步长
            streamline.SetIntegrationDirectionToBoth()
            streamline.Update()

            # 检查是否生成了流线
            if streamline.GetOutput().GetNumberOfPoints() == 0:
                print("警告: 未生成流线，可能速度场为零或种子点位置不当")
                return None

            # 创建流线管道
            tube = vtk.vtkTubeFilter()
            tube.SetInputConnection(streamline.GetOutputPort())
            tube.SetRadius(radius / 100)  # 自适应管道半径
            tube.SetNumberOfSides(6)

            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(tube.GetOutputPort())

            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(1, 0, 0)  # 红色流线

            return actor

        except Exception as e:
            print(f"创建流线可视化失败: {e}")
            return None

    def create_vector_visualization(self, ugrid):
        """创建速度矢量可视化"""
        try:
            # 检查是否有矢量数据
            if ugrid.GetPointData().GetVectors() is None:
                print("警告: 没有找到矢量数据，无法创建矢量可视化")
                return None

            # 创建子采样以减少矢量数量
            subsample = vtk.vtkMaskPoints()
            subsample.SetInputData(ugrid)
            subsample.SetOnRatio(max(1, ugrid.GetNumberOfPoints() // 1000))  # 最多显示1000个矢量
            subsample.RandomModeOn()

            # 创建箭头字形
            arrow = vtk.vtkArrowSource()
            arrow.SetTipResolution(6)
            arrow.SetShaftResolution(6)

            # 创建字形过滤器
            glyph = vtk.vtkGlyph3D()
            glyph.SetInputConnection(subsample.GetOutputPort())
            glyph.SetSourceConnection(arrow.GetOutputPort())
            glyph.SetVectorModeToUseVector()
            glyph.SetScaleModeToScaleByVector()
            glyph.SetScaleFactor(self.vector_scale.get() * 0.01)  # 调整缩放因子

            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(glyph.GetOutputPort())

            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(0, 0, 1)  # 蓝色矢量

            return actor

        except Exception as e:
            print(f"创建矢量可视化失败: {e}")
            return None

    def create_visualization(self):
        """创建主要可视化"""
        # 检查数据是否已加载
        data_source = self.data_source_var.get()
        if data_source == "steady" and self.steady_data is None:
            messagebox.showerror("错误", "请先加载稳态数据")
            return
        elif data_source == "transient" and self.transient_data is None:
            messagebox.showerror("错误", "请先加载瞬态数据")
            return

        try:
            # 获取当前数据
            current_data = self.steady_data if data_source == "steady" else self.transient_data

            # 创建VTK网格
            self.vtk_grid = self.create_vtk_grid(current_data)

            # 添加压力数据
            pressure_col = None
            for col in current_data.columns:
                if 'Pressure' in col and 'Gradient' not in col and 'Total' not in col:
                    pressure_col = col
                    break

            if pressure_col:
                self.add_scalar_data(self.vtk_grid, current_data, pressure_col)

            # 添加速度矢量数据
            velocity_cols = []
            for suffix in ['u', 'v', 'w']:
                for col in current_data.columns:
                    if f'Velocity {suffix}' in col:
                        velocity_cols.append(col)
                        break

            if len(velocity_cols) == 3:
                self.add_vector_data(self.vtk_grid, current_data,
                                   velocity_cols[0], velocity_cols[1], velocity_cols[2],
                                   "Velocity")

            # 创建渲染窗口
            self.setup_vtk_window()

            # 更新可视化
            self.update_visualization()

            self.status_var.set("可视化创建完成")

        except Exception as e:
            messagebox.showerror("错误", f"创建可视化失败: {str(e)}")

    def setup_vtk_window(self):
        """设置VTK渲染窗口"""
        if self.render_window is not None:
            return

        # 创建新窗口
        vtk_window = tk.Toplevel(self.root)
        vtk_window.title("CFX 流体可视化")
        vtk_window.geometry("800x600")

        # 创建VTK渲染组件
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)  # 深蓝色背景

        self.render_window = vtk.vtkRenderWindow()
        self.render_window.AddRenderer(self.renderer)

        # 创建交互器
        self.interactor = vtk.vtkRenderWindowInteractor()
        self.interactor.SetRenderWindow(self.render_window)

        # 设置交互样式
        style = vtk.vtkInteractorStyleTrackballCamera()
        self.interactor.SetInteractorStyle(style)

        # 在Tkinter中嵌入VTK
        try:
            from vtk.tk.vtkTkRenderWindowInteractor import vtkTkRenderWindowInteractor
            vtk_widget = vtkTkRenderWindowInteractor(vtk_window, rw=self.render_window)
            vtk_widget.pack(fill=tk.BOTH, expand=True)
            vtk_widget.Initialize()
            vtk_widget.Start()
        except ImportError:
            # 如果无法嵌入，使用独立窗口
            self.render_window.SetSize(800, 600)
            self.interactor.Initialize()
            self.render_window.Render()
            self.interactor.Start()

    def update_visualization(self):
        """更新可视化显示"""
        if self.vtk_grid is None or self.renderer is None:
            return

        # 清除现有演员
        self.renderer.RemoveAllViewProps()

        try:
            actors_added = 0

            # 添加压力分布
            if self.show_pressure_var.get():
                print("创建压力分布可视化...")
                self.pressure_actor = self.create_pressure_visualization(self.vtk_grid)
                if self.pressure_actor:
                    self.renderer.AddActor(self.pressure_actor)
                    actors_added += 1
                    print("✓ 压力分布已添加")

            # 添加流线
            if self.show_streamlines_var.get():
                print("创建流线可视化...")
                self.streamline_actor = self.create_streamline_visualization(self.vtk_grid)
                if self.streamline_actor:
                    self.renderer.AddActor(self.streamline_actor)
                    actors_added += 1
                    print("✓ 流线已添加")

            # 添加速度矢量
            if self.show_vectors_var.get():
                print("创建矢量可视化...")
                self.velocity_vectors = self.create_vector_visualization(self.vtk_grid)
                if self.velocity_vectors:
                    self.renderer.AddActor(self.velocity_vectors)
                    actors_added += 1
                    print("✓ 速度矢量已添加")

            # 添加坐标轴
            axes = vtk.vtkAxesActor()
            bounds = self.vtk_grid.GetBounds()
            axis_length = min(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4]) / 10
            axes.SetTotalLength(axis_length, axis_length, axis_length)
            self.renderer.AddActor(axes)

            # 添加颜色条（如果显示压力且有压力数据）
            if (self.show_pressure_var.get() and self.pressure_actor and
                self.vtk_grid.GetPointData().GetScalars()):
                try:
                    scalar_bar = vtk.vtkScalarBarActor()
                    scalar_bar.SetLookupTable(self.pressure_actor.GetMapper().GetLookupTable())
                    scalar_bar.SetTitle("压力 [Pa]")
                    scalar_bar.SetNumberOfLabels(5)
                    scalar_bar.SetPosition(0.85, 0.1)
                    scalar_bar.SetWidth(0.1)
                    scalar_bar.SetHeight(0.8)
                    self.renderer.AddActor2D(scalar_bar)
                    print("✓ 颜色条已添加")
                except Exception as e:
                    print(f"添加颜色条失败: {e}")

            # 重置相机
            self.renderer.ResetCamera()

            # 渲染
            if self.render_window:
                self.render_window.Render()

            print(f"可视化更新完成，共添加 {actors_added} 个可视化对象")
            self.status_var.set(f"✓ 可视化已更新 ({actors_added} 个对象)")

        except Exception as e:
            error_msg = f"更新可视化失败: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)

    def save_image(self):
        """保存当前可视化图像"""
        if self.render_window is None:
            messagebox.showerror("错误", "请先创建可视化")
            return

        filename = filedialog.asksaveasfilename(
            title="保存图像",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg"), ("All files", "*.*")]
        )

        if filename:
            try:
                # 创建窗口到图像过滤器
                w2if = vtk.vtkWindowToImageFilter()
                w2if.SetInput(self.render_window)
                w2if.Update()

                # 写入图像
                if filename.lower().endswith('.png'):
                    writer = vtk.vtkPNGWriter()
                elif filename.lower().endswith(('.jpg', '.jpeg')):
                    writer = vtk.vtkJPEGWriter()
                else:
                    writer = vtk.vtkPNGWriter()
                    filename += '.png'

                writer.SetFileName(filename)
                writer.SetInputConnection(w2if.GetOutputPort())
                writer.Write()

                self.status_var.set(f"图像已保存: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"保存图像失败: {str(e)}")

    def export_vtk(self):
        """导出VTK格式文件"""
        if self.vtk_grid is None:
            messagebox.showerror("错误", "请先创建可视化")
            return

        filename = filedialog.asksaveasfilename(
            title="导出VTK文件",
            defaultextension=".vtu",
            filetypes=[("VTU files", "*.vtu"), ("VTK files", "*.vtk"), ("All files", "*.*")]
        )

        if filename:
            try:
                if filename.lower().endswith('.vtu'):
                    writer = vtk.vtkXMLUnstructuredGridWriter()
                else:
                    writer = vtk.vtkUnstructuredGridWriter()

                writer.SetFileName(filename)
                writer.SetInputData(self.vtk_grid)
                writer.Write()

                self.status_var.set(f"VTK文件已导出: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"导出VTK文件失败: {str(e)}")

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    # 检查VTK是否可用
    try:
        import vtk
        print(f"VTK版本: {vtk.vtkVersion.GetVTKVersion()}")
    except ImportError:
        print("错误: 未安装VTK库")
        print("请运行: pip install vtk")
        sys.exit(1)

    # 创建并运行可视化器
    app = CFXVTKVisualizer()
    app.run()


if __name__ == "__main__":
    main()
