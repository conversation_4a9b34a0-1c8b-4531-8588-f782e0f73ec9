# CFX可视化工具

## 概述

CFX流体可视化工具。
## 功能特性

### 核心功能
- **压力分布云图**: 3D压力场可视化，支持颜色映射
- **流速迹线云图**: 基于速度矢量场的流线可视化
- **速度矢量显示**: 3D箭头显示速度方向和大小
- **交互式3D查看**: 支持旋转、缩放、平移操作

### 数据支持
- CFX导出的CSV格式文件
- 稳态和瞬态仿真结果
- 自动解析网格坐标和物理量

### 可视化控制
- 实时调整流线密度
- 速度矢量缩放控制
- 显示选项开关
- 颜色条和坐标轴显示

## 文件结构

```bash
cfx_postprocessing/
├── 数据层/
│   ├── data_models.py          # 数据模型
│   ├── cfx_data_parser.py      # 文件解析器
│   └── data_manager.py         # 数据管理器
├── 可视化层/
│   ├── vtk_renderer.py         # VTK基础渲染器
│   ├── pressure_visualizer.py  # 压力可视化器
│   ├── streamline_visualizer.py # 流线可视化器
│   ├── mesh_visualizer.py      # 网格可视化器
│   └── visualization_manager.py # 可视化管理器
├── 界面层/
│   ├── progress_manager.py     # 进度管理器
│   ├── control_panel.py        # 控制面板
│   └── main_window.py          # 主窗口
├── 测试和示例/
│   ├── 
│   └── 
├── 原始文件/
│   └── vtk_flow_visualizer_qt.py # 原始3300+行文件
├── 文档/
│   ├── README.md        # 本文件 使用说明
│   └── 
├── 配置脚本/
│   └── install_dependencies.py    # 依赖安装脚本
└── demo_data/
    └── case1/
        ├── mesh.csv           # 网格数据
        ├── steady_results.csv # 稳态结果
        └── transient_results.csv # 瞬态结果
```

## 安装和使用

### 1. 安装依赖

```bash
# 自动检查和安装依赖
python install_dependencies.py

# 或手动安装
pip install numpy pandas vtk
```

### 2. 快速开始

## 架构设计

### 📁 数据层 (Data Layer)
- **data_models.py** - 数据模型定义
  - `RegionData` - 区域数据模型，封装数据操作
  - `CoordinateColumns` - 坐标列信息
  - `PhysicalColumns` - 物理量列信息

- **cfx_data_parser.py** - CFX文件解析器
  - `CFXDataParser` - 支持多区域和进度回调的解析器
  - `ProgressCallback` - 进度回调接口

- **data_manager.py** - 数据管理器
  - `DataManager` - 统一管理网格、稳态、瞬态数据

### 📁 可视化层 (Visualization Layer)
- **vtk_renderer.py** - VTK基础渲染器
  - `VTKRenderer` - 提供通用VTK操作的基类

- **pressure_visualizer.py** - 压力可视化器
  - `PressureVisualizer` - 专门处理压力分布和轮廓

- **streamline_visualizer.py** - 流线可视化器
  - `StreamlineVisualizer` - 处理流线和速度矢量

- **mesh_visualizer.py** - 网格可视化器
  - `MeshVisualizer` - 处理网格面片和点云

- **visualization_manager.py** - 可视化管理器
  - `VisualizationManager` - 统一管理所有可视化对象

### 📁 界面层 (UI Layer)
- **progress_manager.py** - 进度管理器
  - `ProgressManager` - 管理进度对话框和回调
  - `ProgressDialog` - 进度条对话框
  - `DataInfoDialog` - 数据信息展示对话框

- **control_panel.py** - 控制面板
  - `ControlPanel` - 用户界面控制组件

- **main_window.py** - 主窗口
  - `CFXVTKVisualizerRefactored` - 简化的主窗口类

## 使用方法

### 1. 运行程序
```bash
python main_window.py
```

### 2. 测试各个模块
```bash
python test_refactored_modules.py
```



## 开发指南

### 添加新的可视化类型
1. 继承 `VTKRenderer` 基类
2. 实现具体的可视化方法
3. 在 `VisualizationManager` 中集成

### 添加新的数据源
1. 扩展 `CFXDataParser` 解析器
2. 在 `DataManager` 中添加管理方法
3. 更新界面控制

### 修改界面布局
1. 修改 `ControlPanel` 组件
2. 在 `MainWindow` 中连接信号
3. 测试界面响应

## 贡献指南

1. 保持模块职责单一
2. 遵循现有的架构模式
3. 添加适当的错误处理
4. 编写单元测试
5. 更新文档

## 许可证

本项目采用与原项目相同的许可证。
