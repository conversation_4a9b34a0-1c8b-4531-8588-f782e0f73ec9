#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据哈希计算工具
统一处理各种数据源的哈希计算，用于缓存管理和变更检测
"""

import hashlib
import json
from typing import List, Dict, Any, Optional, Union
from utils.error_handler import handle_errors, ErrorContext


class DataHashCalculator:
    """数据哈希计算器 - 统一处理各种数据的哈希计算"""
    
    @staticmethod
    @handle_errors(default_return="", log_prefix="数据哈希计算")
    def calculate_flow_data_hash(data_source: str, selected_regions: List[str], 
                                data_manager: Any, sampling_rate: int = 1) -> str:
        """
        计算流体数据哈希值
        
        Args:
            data_source: 数据源标识
            selected_regions: 选中的区域列表
            data_manager: 数据管理器实例
            sampling_rate: 采样率
        
        Returns:
            哈希值字符串
        """
        hash_components = [
            f"flow_data:{data_source}",
            f"regions:{','.join(sorted(selected_regions))}",
            f"sampling_rate:{sampling_rate}"
        ]
        
        # 添加流体数据的详细信息
        if data_manager and data_manager.has_flow_data(data_source):
            flow_data = data_manager.get_flow_data_regions(data_source)
            if flow_data:
                for region_name in sorted(selected_regions):
                    if region_name in flow_data:
                        region_data = flow_data[region_name]
                        region_info = DataHashCalculator._extract_region_info(region_data)
                        hash_components.append(f"region:{region_name}:{region_info}")
        
        return DataHashCalculator._compute_hash(hash_components)
    
    @staticmethod
    @handle_errors(default_return="", log_prefix="网格数据哈希计算")
    def calculate_mesh_data_hash(selected_regions: List[str], data_manager: Any) -> str:
        """
        计算网格数据哈希值
        
        Args:
            selected_regions: 选中的区域列表
            data_manager: 数据管理器实例
        
        Returns:
            哈希值字符串
        """
        hash_components = [
            f"mesh_data",
            f"regions:{','.join(sorted(selected_regions))}"
        ]
        
        # 添加网格数据的详细信息
        if data_manager:
            for region_name in sorted(selected_regions):
                mesh_region = data_manager.get_any_mesh_region(region_name)
                if mesh_region:
                    mesh_info = DataHashCalculator._extract_mesh_info(mesh_region)
                    hash_components.append(f"mesh:{region_name}:{mesh_info}")
        
        return DataHashCalculator._compute_hash(hash_components)
    
    @staticmethod
    @handle_errors(default_return="", log_prefix="可视化参数哈希计算")
    def calculate_visualization_params_hash(params: Dict[str, Any]) -> str:
        """
        计算可视化参数哈希值
        
        Args:
            params: 可视化参数字典
        
        Returns:
            哈希值字符串
        """
        # 标准化参数字典
        normalized_params = DataHashCalculator._normalize_params(params)
        
        # 转换为JSON字符串（确保键的顺序）
        params_str = json.dumps(normalized_params, sort_keys=True)
        
        return DataHashCalculator._compute_hash([f"viz_params:{params_str}"])
    
    @staticmethod
    @handle_errors(default_return="", log_prefix="组合哈希计算")
    def calculate_combined_hash(*hash_values: str) -> str:
        """
        计算多个哈希值的组合哈希
        
        Args:
            *hash_values: 多个哈希值
        
        Returns:
            组合哈希值
        """
        combined = ":".join(filter(None, hash_values))
        return DataHashCalculator._compute_hash([combined])
    
    @staticmethod
    def _extract_region_info(region_data: Any) -> str:
        """提取区域数据信息用于哈希计算"""
        try:
            info_parts = []
            
            # 点数量
            if hasattr(region_data, '__len__'):
                info_parts.append(f"points:{len(region_data)}")
            elif hasattr(region_data, 'points') and region_data.points is not None:
                info_parts.append(f"points:{len(region_data.points)}")
            
            # 面片数量
            if hasattr(region_data, 'has_faces') and region_data.has_faces():
                if hasattr(region_data, 'faces') and region_data.faces is not None:
                    info_parts.append(f"faces:{len(region_data.faces)}")
            
            # 物理场信息
            if hasattr(region_data, 'get_available_physical_fields'):
                fields = region_data.get_available_physical_fields()
                if fields:
                    field_names = sorted(fields.keys())
                    info_parts.append(f"fields:{','.join(field_names)}")
            
            return "|".join(info_parts) if info_parts else "empty"
            
        except Exception as e:
            print(f"提取区域信息失败: {e}")
            return "error"
    
    @staticmethod
    def _extract_mesh_info(mesh_region: Any) -> str:
        """提取网格信息用于哈希计算"""
        try:
            info_parts = []
            
            # 点数量
            if hasattr(mesh_region, 'points') and mesh_region.points is not None:
                info_parts.append(f"points:{len(mesh_region.points)}")
            
            # 单元数量
            if hasattr(mesh_region, 'cells') and mesh_region.cells is not None:
                info_parts.append(f"cells:{len(mesh_region.cells)}")
            
            # 边界信息
            if hasattr(mesh_region, 'get_bounds'):
                bounds = mesh_region.get_bounds()
                if bounds:
                    bounds_str = ",".join(f"{b:.6f}" for b in bounds)
                    info_parts.append(f"bounds:{bounds_str}")
            
            return "|".join(info_parts) if info_parts else "empty"
            
        except Exception as e:
            print(f"提取网格信息失败: {e}")
            return "error"
    
    @staticmethod
    def _normalize_params(params: Dict[str, Any]) -> Dict[str, Any]:
        """标准化参数字典"""
        normalized = {}
        
        for key, value in params.items():
            if isinstance(value, (list, tuple)):
                # 列表和元组转换为排序后的元组
                normalized[key] = tuple(sorted(value) if all(isinstance(x, (str, int, float)) for x in value) else value)
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                normalized[key] = DataHashCalculator._normalize_params(value)
            elif isinstance(value, float):
                # 浮点数保留6位小数
                normalized[key] = round(value, 6)
            else:
                normalized[key] = value
        
        return normalized
    
    @staticmethod
    def _compute_hash(components: List[str]) -> str:
        """计算哈希值"""
        try:
            # 组合所有组件
            combined_data = ":".join(components)
            
            # 计算MD5哈希
            hash_object = hashlib.md5(combined_data.encode('utf-8'))
            return hash_object.hexdigest()
            
        except Exception as e:
            print(f"计算哈希失败: {e}")
            return ""


class CacheManager:
    """缓存管理器 - 基于哈希值的缓存管理"""
    
    def __init__(self):
        self.cache_hashes: Dict[str, str] = {}
        self.cache_data: Dict[str, Any] = {}
    
    @handle_errors(default_return=False, log_prefix="缓存检查")
    def is_cache_valid(self, cache_key: str, current_hash: str) -> bool:
        """
        检查缓存是否有效
        
        Args:
            cache_key: 缓存键
            current_hash: 当前数据哈希
        
        Returns:
            缓存是否有效
        """
        if cache_key not in self.cache_hashes:
            return False
        
        stored_hash = self.cache_hashes[cache_key]
        is_valid = stored_hash == current_hash and cache_key in self.cache_data
        
        if is_valid:
            print(f"✓ 缓存有效: {cache_key}")
        else:
            print(f"○ 缓存无效: {cache_key}")
        
        return is_valid
    
    @handle_errors(default_return=None, log_prefix="缓存获取")
    def get_cached_data(self, cache_key: str) -> Any:
        """获取缓存数据"""
        return self.cache_data.get(cache_key)
    
    @handle_errors(log_prefix="缓存更新")
    def update_cache(self, cache_key: str, data_hash: str, data: Any):
        """更新缓存"""
        self.cache_hashes[cache_key] = data_hash
        self.cache_data[cache_key] = data
        print(f"✓ 缓存已更新: {cache_key}")
    
    def clear_cache(self, cache_key: Optional[str] = None):
        """清除缓存"""
        if cache_key:
            self.cache_hashes.pop(cache_key, None)
            self.cache_data.pop(cache_key, None)
            print(f"✓ 缓存已清除: {cache_key}")
        else:
            self.cache_hashes.clear()
            self.cache_data.clear()
            print("✓ 所有缓存已清除")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_count': len(self.cache_hashes),
            'cache_keys': list(self.cache_hashes.keys()),
            'total_size': sum(1 for _ in self.cache_data.values())  # 简化的大小计算
        }
