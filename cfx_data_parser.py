#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFX数据解析器
负责解析CFX CSV文件格式，支持多区域数据
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Optional, Callable, Any
from data_models import RegionData


class ProgressCallback:
    """进度回调接口"""
    
    def __init__(self, callback: Optional[Callable[[int, str, str], None]] = None):
        self.callback = callback
        self.cancelled = False
        
    def update(self, progress: int, message: str = "", detail: str = ""):
        """更新进度"""
        if self.callback:
            self.callback(progress, message, detail)
            
    def is_cancelled(self) -> bool:
        """检查是否被取消"""
        return self.cancelled
        
    def cancel(self):
        """取消操作"""
        self.cancelled = True


class CFXDataParser:
    """CFX CSV文件解析器"""
    
    def __init__(self):
        self.encoding_options = ['utf-8', 'latin-1', 'gbk', 'cp1252']
        
    def parse_file(self, filename: str, progress_callback: Optional[ProgressCallback] = None) -> Dict[str, RegionData]:
        """
        解析CFX CSV文件

        Args:
            filename: 文件路径
            progress_callback: 进度回调

        Returns:
            Dict[str, RegionData]: 区域名称到RegionData的映射
        """
        if progress_callback:
            progress_callback.update(5, "正在读取文件...")

        try:
            regions = self._parse_file_robust(filename, progress_callback)
            if regions:
                if progress_callback:
                    progress_callback.update(100, "解析完成")
                return regions
        except Exception as e:
            print(f"文件解析失败: {e}")
            return {}

    def _parse_file_robust(self, filename: str, progress_callback: Optional[ProgressCallback] = None) -> Dict[str, RegionData]:
        """
        按照原始格式解析，基于正则表达式精确分割区块
        """
        if progress_callback:
            progress_callback.update(10, "正在读取文件...")

        # 读取文件内容，尝试多种编码
        content = None
        for encoding in self.encoding_options:
            try:
                with open(filename, 'r', encoding=encoding) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文件失败 (编码: {encoding}): {e}")
                continue

        if content is None:
            raise ValueError("无法读取文件，尝试了所有编码选项")

        if progress_callback:
            progress_callback.update(20, "文件读取完成，开始解析...")

        # 使用正则表达式精确分割区块
        blocks = re.split(r'\[Name\]\s*', content)[1:]

        if not blocks:
            raise ValueError("未找到任何区域数据块")

        regions = {}
        offset = 0
        total_blocks = len(blocks)

        for i, block in enumerate(blocks):
            if progress_callback and progress_callback.is_cancelled():
                return {}

            # 更新进度
            if progress_callback:
                progress = 20 + int((i / total_blocks) * 70)
                progress_callback.update(progress, f"处理区域数据 {i+1}/{total_blocks}")

            # 使用更安全的方式分割数据段
            data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
            if len(data_parts) < 2:
                continue
            header = data_parts[0].strip()
            name = header.splitlines()[0].strip()
            remaining = data_parts[1]

            # 分割面和数据
            face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
            if len(face_parts) < 2:
                data_section = face_parts[0]
                faces_section = ''
            else:
                data_section = face_parts[0]
                faces_section = face_parts[1]

            # 处理数据部分
            data_lines = [line.strip() for line in data_section.split('\n')
                         if line.strip()]
            if not data_lines:
                continue

            header = [h.strip() for h in data_lines[0].split(',')]

            try:
                data = np.array([list(map(float, line.split(',')))
                               for line in data_lines[1:]])
                df = pd.DataFrame(data, columns=header)

                # 创建区域数据
                region_data = RegionData(name, df)

                # 处理面数据
                # faces = []
                ori_faces = []
                for line in faces_section.split('\n'):
                    line = line.strip()
                    if line:
                        try:
                            ori_face = list(map(int, line.split(',')))
                            # face = [(it + offset) for it in ori_face]
                            ori_faces.append(ori_face)
                            # faces.append(face)
                        except (ValueError, IndexError):
                            continue

                region_data.set_faces(ori_faces)

                offset += len(data)
                regions[name] = region_data

                print(f"区域 {name}: 解析完成，数据点 {len(data)}，面片 {len(ori_faces)}")

            except Exception as e:
                print(f"解析区域 {name} 失败: {e}")
                continue

        print(f"解析完成，共处理了 {len(regions)} 个区域")
        return regions
